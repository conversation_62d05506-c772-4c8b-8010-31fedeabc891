import React, { useState, useRef } from 'react';
import { X, Download, Share2, FileText, Image, Link, Mail, Printer } from 'lucide-react';

interface ExportShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  systemType: string;
  selectedComponent: string | null;
}

const ExportShareModal = ({ isOpen, onClose, systemType, selectedComponent }: ExportShareModalProps) => {
  const [exportFormat, setExportFormat] = useState<'png' | 'svg' | 'pdf'>('png');
  const [includeSpecs, setIncludeSpecs] = useState(true);
  const [includeMonitoring, setIncludeMonitoring] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [isGeneratingShare, setIsGeneratingShare] = useState(false);

  if (!isOpen) return null;

  const handleExportDiagram = async (format: 'png' | 'svg' | 'pdf') => {
    const diagramElement = document.querySelector('.hvac-diagram svg') as SVGElement;
    if (!diagramElement) return;

    try {
      if (format === 'svg') {
        // Export as SVG
        const svgData = new XMLSerializer().serializeToString(diagramElement);
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `hvac-${systemType}-diagram.svg`;
        link.click();
        URL.revokeObjectURL(url);
      } else if (format === 'png') {
        // Export as PNG
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        canvas.width = 1920;
        canvas.height = 1080;
        
        const svgData = new XMLSerializer().serializeToString(diagramElement);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);
        
        img.onload = () => {
          if (ctx) {
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            canvas.toBlob((blob) => {
              if (blob) {
                const downloadUrl = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = `hvac-${systemType}-diagram.png`;
                link.click();
                URL.revokeObjectURL(downloadUrl);
              }
            });
          }
          URL.revokeObjectURL(url);
        };
        
        img.src = url;
      } else if (format === 'pdf') {
        // For PDF export, we'll use the PNG method and then convert
        // In a real implementation, you'd use a library like jsPDF
        alert('PDF export would be implemented with a library like jsPDF');
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    }
  };

  const handleGenerateShareLink = async () => {
    setIsGeneratingShare(true);
    
    // Simulate generating a shareable link
    setTimeout(() => {
      const shareId = Math.random().toString(36).substring(7);
      const url = `${window.location.origin}/shared/${shareId}?system=${systemType}&component=${selectedComponent || 'none'}`;
      setShareUrl(url);
      setIsGeneratingShare(false);
    }, 1500);
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareUrl);
    alert('Link copied to clipboard!');
  };

  const handleEmailShare = () => {
    const subject = `HVAC System Diagram - ${systemType}`;
    const body = `Check out this HVAC system diagram: ${shareUrl}`;
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const diagramElement = document.querySelector('.hvac-diagram svg');
      if (diagramElement) {
        const svgData = new XMLSerializer().serializeToString(diagramElement as SVGElement);
        printWindow.document.write(`
          <html>
            <head>
              <title>HVAC System Diagram - ${systemType}</title>
              <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                .header { text-align: center; margin-bottom: 20px; }
                .diagram { text-align: center; }
                svg { max-width: 100%; height: auto; }
                @media print {
                  body { margin: 0; }
                  .header { page-break-inside: avoid; }
                }
              </style>
            </head>
            <body>
              <div class="header">
                <h1>HVAC System Diagram</h1>
                <h2>${systemType.replace('-', ' ').toUpperCase()}</h2>
                <p>Generated on ${new Date().toLocaleDateString()}</p>
              </div>
              <div class="diagram">
                ${svgData}
              </div>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const generateTechnicalReport = () => {
    const systemData = {
      'split-system': {
        name: 'Split System Air Conditioner',
        capacity: '36,000 BTU/hr',
        efficiency: 'SEER 16.0, EER 12.8',
        refrigerant: 'R-410A (3.9 lbs)',
        components: ['Variable Speed Compressor', 'Microchannel Condenser', 'A-Frame Evaporator', 'TXV']
      },
      'heat-pump': {
        name: 'Heat Pump System',
        capacity: '36,000 BTU/hr Cooling, 38,000 BTU/hr Heating',
        efficiency: 'SEER 16.0, HSPF 10.5',
        refrigerant: 'R-410A (4.2 lbs)',
        components: ['Variable Speed Compressor', 'Reversing Valve', 'Outdoor Coil', 'Indoor Coil']
      },
      'packaged-unit': {
        name: 'Packaged Rooftop Unit',
        capacity: '120,000 BTU/hr Cooling, 180,000 BTU/hr Heating',
        efficiency: 'EER 9.6, 90% AFUE',
        refrigerant: 'R-410A (12 lbs)',
        components: ['Scroll Compressor', 'Gas Heat Exchanger', 'Economizer', 'VFD Controls']
      }
    };

    const data = systemData[systemType as keyof typeof systemData];
    
    const reportContent = `
HVAC SYSTEM TECHNICAL REPORT
============================

System Type: ${data.name}
Date Generated: ${new Date().toLocaleDateString()}
Time: ${new Date().toLocaleTimeString()}

SYSTEM SPECIFICATIONS
--------------------
Capacity: ${data.capacity}
Efficiency: ${data.efficiency}
Refrigerant: ${data.refrigerant}

MAJOR COMPONENTS
---------------
${data.components.map(comp => `• ${comp}`).join('\n')}

OPERATING CONDITIONS
-------------------
• Outdoor Design Temperature: 95°F DB / 75°F WB
• Indoor Design Temperature: 75°F DB / 62.5°F WB
• Refrigerant Operating Pressures: 278 PSI High / 118 PSI Low
• Superheat: 8-12°F
• Subcooling: 10-15°F

PERFORMANCE DATA
---------------
• Current EER: 12.8
• Power Draw: 2.85 kW
• Airflow: 1,200 CFM
• Temperature Difference: 20°F

This report was generated by the HVAC Diagram System.
For technical support, contact your HVAC professional.
    `;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `hvac-${systemType}-report.txt`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Export & Share</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X size={24} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Export Options */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Download className="mr-2" size={20} />
              Export Diagram
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Export Format
                </label>
                <div className="flex space-x-4">
                  {(['png', 'svg', 'pdf'] as const).map((format) => (
                    <label key={format} className="flex items-center">
                      <input
                        type="radio"
                        value={format}
                        checked={exportFormat === format}
                        onChange={(e) => setExportFormat(e.target.value as 'png' | 'svg' | 'pdf')}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {format.toUpperCase()}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={() => handleExportDiagram(exportFormat)}
                  className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Image size={16} />
                  <span>Export Diagram</span>
                </button>
                
                <button
                  onClick={generateTechnicalReport}
                  className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  <FileText size={16} />
                  <span>Technical Report</span>
                </button>
                
                <button
                  onClick={handlePrint}
                  className="flex items-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <Printer size={16} />
                  <span>Print</span>
                </button>
              </div>
            </div>
          </div>

          {/* Share Options */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Share2 className="mr-2" size={20} />
              Share Diagram
            </h3>
            
            <div className="space-y-4">
              {!shareUrl ? (
                <button
                  onClick={handleGenerateShareLink}
                  disabled={isGeneratingShare}
                  className="flex items-center space-x-2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  <Link size={16} />
                  <span>{isGeneratingShare ? 'Generating...' : 'Generate Share Link'}</span>
                </button>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={shareUrl}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm"
                    />
                    <button
                      onClick={handleCopyLink}
                      className="px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      Copy
                    </button>
                  </div>
                  
                  <button
                    onClick={handleEmailShare}
                    className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Mail size={16} />
                    <span>Share via Email</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportShareModal;
