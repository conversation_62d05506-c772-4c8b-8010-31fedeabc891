import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, AlertTriangle, CheckCircle, Activity, Zap, Thermometer, Gauge } from 'lucide-react';

interface MonitoringData {
  timestamp: Date;
  highPressure: number;
  lowPressure: number;
  dischargeTemp: number;
  suctionTemp: number;
  superheat: number;
  subcooling: number;
  powerDraw: number;
  efficiency: number;
  capacity: number;
  airflow: number;
  ambientTemp: number;
  returnAirTemp: number;
  supplyAirTemp: number;
}

interface SystemMonitoringDashboardProps {
  isAnimating: boolean;
  systemType: string;
}

const SystemMonitoringDashboard = ({ isAnimating, systemType }: SystemMonitoringDashboardProps) => {
  const [currentData, setCurrentData] = useState<MonitoringData>({
    timestamp: new Date(),
    highPressure: 278,
    lowPressure: 118,
    dischargeTemp: 165,
    suctionTemp: 48,
    superheat: 8.2,
    subcooling: 12.5,
    powerDraw: 2.85,
    efficiency: 12.8,
    capacity: 36.5,
    airflow: 1200,
    ambientTemp: 95,
    returnAirTemp: 75,
    supplyAirTemp: 55
  });

  const [historicalData, setHistoricalData] = useState<MonitoringData[]>([]);
  const [alerts, setAlerts] = useState<string[]>([]);

  // Simulate real-time data updates
  useEffect(() => {
    if (!isAnimating) return;

    const interval = setInterval(() => {
      const newData: MonitoringData = {
        timestamp: new Date(),
        highPressure: currentData.highPressure + (Math.random() - 0.5) * 6,
        lowPressure: currentData.lowPressure + (Math.random() - 0.5) * 4,
        dischargeTemp: currentData.dischargeTemp + (Math.random() - 0.5) * 8,
        suctionTemp: currentData.suctionTemp + (Math.random() - 0.5) * 4,
        superheat: Math.max(5, Math.min(15, currentData.superheat + (Math.random() - 0.5) * 1.5)),
        subcooling: Math.max(8, Math.min(18, currentData.subcooling + (Math.random() - 0.5) * 1.5)),
        powerDraw: Math.max(2.2, Math.min(3.8, currentData.powerDraw + (Math.random() - 0.5) * 0.15)),
        efficiency: Math.max(10, Math.min(15, currentData.efficiency + (Math.random() - 0.5) * 0.3)),
        capacity: Math.max(32, Math.min(40, currentData.capacity + (Math.random() - 0.5) * 0.8)),
        airflow: Math.max(1100, Math.min(1300, currentData.airflow + (Math.random() - 0.5) * 60)),
        ambientTemp: Math.max(90, Math.min(100, currentData.ambientTemp + (Math.random() - 0.5) * 2)),
        returnAirTemp: Math.max(72, Math.min(78, currentData.returnAirTemp + (Math.random() - 0.5) * 1)),
        supplyAirTemp: Math.max(52, Math.min(58, currentData.supplyAirTemp + (Math.random() - 0.5) * 1))
      };

      setCurrentData(newData);
      setHistoricalData(prev => [...prev.slice(-19), newData]); // Keep last 20 data points

      // Check for alerts
      const newAlerts: string[] = [];
      if (newData.superheat < 6 || newData.superheat > 14) {
        newAlerts.push(`Superheat out of range: ${newData.superheat.toFixed(1)}°F`);
      }
      if (newData.subcooling < 9 || newData.subcooling > 17) {
        newAlerts.push(`Subcooling out of range: ${newData.subcooling.toFixed(1)}°F`);
      }
      if (newData.highPressure > 300 || newData.highPressure < 250) {
        newAlerts.push(`High pressure abnormal: ${newData.highPressure.toFixed(0)} PSI`);
      }
      if (newData.efficiency < 11) {
        newAlerts.push(`Low efficiency: ${newData.efficiency.toFixed(1)} EER`);
      }
      setAlerts(newAlerts);
    }, 2000);

    return () => clearInterval(interval);
  }, [isAnimating, currentData]);

  const getStatusColor = (value: number, min: number, max: number) => {
    if (value >= min && value <= max) return 'text-green-600';
    if (value >= min * 0.9 && value <= max * 1.1) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (value: number, min: number, max: number) => {
    if (value >= min && value <= max) return <CheckCircle size={16} className="text-green-600" />;
    if (value >= min * 0.9 && value <= max * 1.1) return <AlertTriangle size={16} className="text-yellow-600" />;
    return <AlertTriangle size={16} className="text-red-600" />;
  };

  const calculateCOP = () => {
    // COP = Cooling Capacity (BTU/hr) / Power Input (W) / 3.412
    return (currentData.capacity * 1000) / (currentData.powerDraw * 1000) / 3.412;
  };

  const calculateTonnage = () => {
    return currentData.capacity / 12; // BTU/hr to tons
  };

  return (
    <div className="space-y-6">
      {/* System Status Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">System Status</span>
            {alerts.length === 0 ? (
              <CheckCircle size={20} className="text-green-600" />
            ) : (
              <AlertTriangle size={20} className="text-red-600" />
            )}
          </div>
          <div className={`text-lg font-bold ${alerts.length === 0 ? 'text-green-600' : 'text-red-600'}`}>
            {alerts.length === 0 ? 'NORMAL' : 'ALERT'}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Efficiency</span>
            <Gauge size={20} className="text-blue-600" />
          </div>
          <div className={`text-lg font-bold ${getStatusColor(currentData.efficiency, 11, 15)}`}>
            {currentData.efficiency.toFixed(1)} EER
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Capacity</span>
            <Activity size={20} className="text-purple-600" />
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {calculateTonnage().toFixed(1)} Tons
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Power Draw</span>
            <Zap size={20} className="text-yellow-600" />
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {currentData.powerDraw.toFixed(2)} kW
          </div>
        </div>
      </div>

      {/* Critical Parameters */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Critical Parameters</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Superheat</span>
              {getStatusIcon(currentData.superheat, 6, 14)}
            </div>
            <div className={`text-xl font-bold ${getStatusColor(currentData.superheat, 6, 14)}`}>
              {currentData.superheat.toFixed(1)}°F
            </div>
            <div className="text-xs text-gray-500">Target: 8-12°F</div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Subcooling</span>
              {getStatusIcon(currentData.subcooling, 9, 17)}
            </div>
            <div className={`text-xl font-bold ${getStatusColor(currentData.subcooling, 9, 17)}`}>
              {currentData.subcooling.toFixed(1)}°F
            </div>
            <div className="text-xs text-gray-500">Target: 10-15°F</div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">High Pressure</span>
              {getStatusIcon(currentData.highPressure, 250, 300)}
            </div>
            <div className={`text-xl font-bold ${getStatusColor(currentData.highPressure, 250, 300)}`}>
              {currentData.highPressure.toFixed(0)} PSI
            </div>
            <div className="text-xs text-gray-500">Normal: 250-300 PSI</div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Low Pressure</span>
              {getStatusIcon(currentData.lowPressure, 110, 130)}
            </div>
            <div className={`text-xl font-bold ${getStatusColor(currentData.lowPressure, 110, 130)}`}>
              {currentData.lowPressure.toFixed(0)} PSI
            </div>
            <div className="text-xs text-gray-500">Normal: 110-130 PSI</div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Metrics</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          <div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Coefficient of Performance</div>
            <div className="text-2xl font-bold text-blue-600">{calculateCOP().toFixed(2)}</div>
            <div className="text-xs text-gray-500">Higher is better</div>
          </div>

          <div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Temperature Difference</div>
            <div className="text-2xl font-bold text-green-600">
              {(currentData.returnAirTemp - currentData.supplyAirTemp).toFixed(1)}°F
            </div>
            <div className="text-xs text-gray-500">Return - Supply</div>
          </div>

          <div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Airflow Rate</div>
            <div className="text-2xl font-bold text-purple-600">{currentData.airflow.toFixed(0)} CFM</div>
            <div className="text-xs text-gray-500">
              {(currentData.airflow / calculateTonnage() / 400).toFixed(1)} CFM/Ton
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <AlertTriangle size={20} className="text-red-600 mr-2" />
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-400">System Alerts</h3>
          </div>
          <ul className="space-y-1">
            {alerts.map((alert, index) => (
              <li key={index} className="text-sm text-red-700 dark:text-red-300">
                • {alert}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Data Timestamp */}
      <div className="text-xs text-gray-500 text-center">
        Last updated: {currentData.timestamp.toLocaleTimeString()}
        {isAnimating && <span className="ml-2 text-green-600">● Live</span>}
      </div>
    </div>
  );
};

export default SystemMonitoringDashboard;
