import React, { useState } from 'react';
import { Info, Play, Pause, RotateCcw, Thermometer, Gauge, Zap, Maximize2, Activity, Eye, Monitor, Share2 } from 'lucide-react';
import GlassCard from '../components/GlassCard';
import HVACDiagram from '../components/HVACDiagram';
import FullScreenDiagram from '../components/FullScreenDiagram';
import InteractiveMeasurementTools from '../components/InteractiveMeasurementTools';
import ComponentDetailView from '../components/ComponentDetailView';
import SystemMonitoringDashboard from '../components/SystemMonitoringDashboard';
import ExportShareModal from '../components/ExportShareModal';

const DiagramsPage = () => {
  const [selectedSystem, setSelectedSystem] = useState('split-system');
  const [isAnimating, setIsAnimating] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [showMeasurementTools, setShowMeasurementTools] = useState(true);
  const [showDetailView, setShowDetailView] = useState(false);
  const [activeTab, setActiveTab] = useState<'diagram' | 'monitoring'>('diagram');
  const [showExportModal, setShowExportModal] = useState(false);

  const systems = [
    {
      id: 'split-system',
      name: 'Split System AC',
      description: 'Residential cooling system with separate indoor and outdoor units',
      difficulty: 'Beginner',
      components: 4
    },
    {
      id: 'heat-pump',
      name: 'Heat Pump System',
      description: 'Reversible refrigeration system for heating and cooling',
      difficulty: 'Intermediate',
      components: 6
    },
    {
      id: 'packaged-unit',
      name: 'Packaged RTU',
      description: 'Commercial rooftop unit with integrated components',
      difficulty: 'Advanced',
      components: 8
    }
  ];

  const componentInfo = {
    'evaporator': {
      title: 'A-Frame Evaporator Coil',
      description: 'Microchannel aluminum heat exchanger optimized for R-410A refrigerant with enhanced heat transfer efficiency and reduced refrigerant charge.',
      specifications: [
        'Operating Pressure: 118-125 PSI @ 40°F (R-410A)',
        'Saturation Temperature: 40°F @ 118 PSI',
        'Target Superheat: 8-12°F (adjustable)',
        'Material: Copper tubes, aluminum fins, 14 FOPI',
        'Face Area: 3.5 sq ft, 2-row deep',
        'Capacity: 36,000 BTU/hr @ ARI conditions',
        'Refrigerant Charge: 2.1 lbs R-410A'
      ],
      function: 'Absorbs sensible heat (temperature reduction) and latent heat (moisture removal) from indoor air through refrigerant evaporation at constant pressure and temperature.'
    },
    'condenser': {
      title: 'Microchannel Condenser Coil',
      description: 'High-efficiency aluminum microchannel condenser with optimized fin design for maximum heat rejection and reduced refrigerant charge.',
      specifications: [
        'Operating Pressure: 278-295 PSI @ 95°F ambient (R-410A)',
        'Saturation Temperature: 105°F @ 278 PSI',
        'Target Subcooling: 10-15°F',
        'Material: All-aluminum construction, louvered fins',
        'Face Area: 8.2 sq ft, 1-row microchannel',
        'Heat Rejection: 42,000 BTU/hr @ ARI conditions',
        'Refrigerant Charge: 1.8 lbs R-410A'
      ],
      function: 'Rejects heat from high-pressure refrigerant vapor to ambient air, condensing vapor to liquid while removing superheat and latent heat of vaporization.'
    },
    'compressor': {
      title: 'Variable Speed Scroll Compressor',
      description: 'Inverter-driven scroll compressor with variable capacity control, optimized for R-410A refrigerant with enhanced efficiency and reduced sound levels.',
      specifications: [
        'Type: Hermetic scroll with inverter drive',
        'Capacity Range: 40-110% (variable speed)',
        'Power Input: 2.8-4.2 kW (3.8-5.6 HP)',
        'Voltage: 208-230V/1Ph/60Hz',
        'Compression Ratio: 2.4:1 @ ARI conditions',
        'EER: 12.5 @ full load, 16.8 @ part load',
        'Sound Level: 72 dB @ 10 ft'
      ],
      function: 'Compresses low-pressure refrigerant vapor to high pressure while adding energy to maintain refrigerant circulation and system pressure differential.'
    },
    'expansion-valve': {
      title: 'Thermostatic Expansion Valve (TXV)',
      description: 'Precision metering device with thermal sensing bulb for automatic superheat control, optimized for R-410A operating pressures.',
      specifications: [
        'Type: Externally equalized TXV',
        'Capacity: 3.0 tons @ 40°F evap, 105°F cond',
        'Superheat Setting: 8°F factory (adjustable 6-12°F)',
        'Pressure Drop: 160 PSI @ rated capacity',
        'Response Time: 45-60 seconds',
        'Operating Range: -40°F to 200°F',
        'Sensing Bulb: R-410A charge'
      ],
      function: 'Maintains constant superheat by modulating refrigerant flow rate, ensuring optimal evaporator performance while preventing liquid refrigerant return to compressor.'
    },
    'outdoor-coil': {
      title: 'Outdoor Heat Exchanger',
      description: 'Dual-function coil that operates as condenser in cooling mode and evaporator in heating mode, with reversible refrigerant flow.',
      specifications: [
        'Cooling Mode: 278 PSI, 105°F condensing',
        'Heating Mode: 118 PSI, 25°F evaporating',
        'Material: Aluminum microchannel',
        'Face Area: 12.5 sq ft',
        'Defrost: Hot gas bypass, 90 seconds',
        'Operating Range: -10°F to 115°F ambient'
      ],
      function: 'Provides heat rejection in cooling mode and heat absorption in heating mode through reversible refrigerant flow control.'
    },
    'indoor-coil': {
      title: 'Indoor Heat Exchanger',
      description: 'Dual-function coil that operates as evaporator in cooling mode and condenser in heating mode, optimized for comfort conditioning.',
      specifications: [
        'Cooling Mode: 118 PSI, 40°F evaporating',
        'Heating Mode: 278 PSI, 110°F condensing',
        'Material: Copper tubes, aluminum fins',
        'Face Area: 4.2 sq ft, A-frame design',
        'Capacity: 36,000 BTU/hr cooling, 38,000 BTU/hr heating'
      ],
      function: 'Provides space cooling through heat absorption or space heating through heat rejection, depending on operating mode.'
    },
    'reversing-valve': {
      title: '4-Way Reversing Valve',
      description: 'Solenoid-operated valve that reverses refrigerant flow direction to switch between heating and cooling modes.',
      specifications: [
        'Type: Pilot-operated, 4-way valve',
        'Operating Pressure: 400 PSI max',
        'Response Time: 3-5 seconds',
        'Voltage: 24V AC solenoid',
        'Flow Capacity: 3-5 tons',
        'Leak Rate: <0.5 oz/year'
      ],
      function: 'Controls refrigerant flow direction to enable heat pump operation in both heating and cooling modes.'
    },
    'heat-exchanger': {
      title: 'Primary Heat Exchanger',
      description: 'Stainless steel heat exchanger for natural gas combustion with high efficiency and low NOx emissions.',
      specifications: [
        'Input: 200,000 BTU/hr natural gas',
        'Output: 180,000 BTU/hr (90% efficiency)',
        'Material: 409 stainless steel',
        'Heat Transfer: Serpentine tube design',
        'Emissions: <40 ppm NOx',
        'Warranty: 20 years limited'
      ],
      function: 'Transfers heat from natural gas combustion to supply air for space heating in commercial applications.'
    },
    'outside-air-damper': {
      title: 'Outside Air Damper',
      description: 'Modulating damper assembly for economizer operation and minimum outside air ventilation requirements.',
      specifications: [
        'Type: Parallel blade, modulating',
        'Control: 0-10V or 4-20mA actuator',
        'Leakage: Class 1A per AMCA 500-D',
        'Operating Range: 0-100% open',
        'Response Time: 60 seconds full stroke',
        'Materials: Aluminum frame, EPDM seals'
      ],
      function: 'Controls outside air intake for ventilation, economizer cooling, and building pressurization.'
    },
    'return-air-damper': {
      title: 'Return Air Damper',
      description: 'Modulating damper for return air control in economizer and ventilation applications.',
      specifications: [
        'Type: Opposed blade, modulating',
        'Control: Linked to OA damper operation',
        'Leakage: Class 1A per AMCA 500-D',
        'Operating Range: 0-90% open',
        'Materials: Aluminum construction',
        'Seals: EPDM weather sealing'
      ],
      function: 'Modulates return air flow to maintain proper air balance during economizer and ventilation operation.'
    },
    'exhaust-damper': {
      title: 'Relief/Exhaust Damper',
      description: 'Gravity or motorized damper for building pressure relief and economizer exhaust air discharge.',
      specifications: [
        'Type: Gravity relief or motorized',
        'Control: Building pressure or economizer',
        'Opening Pressure: 0.05" WC (gravity)',
        'Materials: Aluminum frame and blades',
        'Weather Sealing: EPDM gaskets',
        'Bird Screen: 1/2" mesh included'
      ],
      function: 'Provides building pressure relief and exhaust air discharge during economizer operation.'
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'Intermediate': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'Advanced': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            HVAC System Diagrams
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl">
            Interactive technical diagrams with accurate component specifications, 
            animated refrigerant flow, and detailed system operation analysis.
          </p>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* System Selection */}
          <div className="lg:col-span-1 space-y-6">
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Type</h3>
              <div className="space-y-3">
                {systems.map((system) => (
                  <button
                    key={system.id}
                    onClick={() => {
                      setSelectedSystem(system.id);
                      setSelectedComponent(null);
                      setIsAnimating(false);
                    }}
                    className={`w-full text-left p-4 rounded-lg border transition-all duration-200 ${
                      selectedSystem === system.id
                        ? 'border-accent-600 bg-accent-50 dark:bg-accent-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <div className="font-semibold text-gray-900 dark:text-white mb-1">{system.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">{system.description}</div>
                    <div className="flex items-center justify-between">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getDifficultyColor(system.difficulty)}`}>
                        {system.difficulty}
                      </span>
                      <span className="text-xs text-gray-500">{system.components} components</span>
                    </div>
                  </button>
                ))}
              </div>
            </GlassCard>

            {/* Controls */}
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Controls</h3>
              <div className="space-y-3">
                <button
                  onClick={() => setIsAnimating(!isAnimating)}
                  className="w-full flex items-center justify-center space-x-2 bg-accent-600 text-white px-4 py-3 rounded-lg hover:bg-accent-700 transition-colors duration-200"
                >
                  {isAnimating ? <Pause size={18} /> : <Play size={18} />}
                  <span>{isAnimating ? 'Pause Flow' : 'Start Flow'}</span>
                </button>
                <button
                  onClick={() => setIsFullScreen(true)}
                  className="w-full flex items-center justify-center space-x-2 bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200"
                >
                  <Maximize2 size={18} />
                  <span>Full Screen</span>
                </button>
                <button
                  onClick={() => setShowMeasurementTools(!showMeasurementTools)}
                  className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-colors duration-200 ${
                    showMeasurementTools
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                  }`}
                >
                  <Activity size={18} />
                  <span>{showMeasurementTools ? 'Hide Tools' : 'Show Tools'}</span>
                </button>
                <button
                  onClick={() => setShowExportModal(true)}
                  className="w-full flex items-center justify-center space-x-2 bg-indigo-600 text-white px-4 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200"
                >
                  <Share2 size={18} />
                  <span>Export & Share</span>
                </button>
                <button
                  onClick={() => {
                    setIsAnimating(false);
                    setSelectedComponent(null);
                    setSelectedTool(null);
                  }}
                  className="w-full flex items-center justify-center space-x-2 bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-200"
                >
                  <RotateCcw size={18} />
                  <span>Reset View</span>
                </button>
              </div>
            </GlassCard>

            {/* Legend */}
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Legend</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">High pressure, hot gas</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">High pressure, warm liquid</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">Low pressure, cold liquid</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-cyan-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">Low pressure, cool vapor</span>
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-2">
            <GlassCard className="p-6">
              {/* Tab Navigation */}
              <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                <button
                  onClick={() => setActiveTab('diagram')}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                    activeTab === 'diagram'
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <Eye size={18} />
                  <span>System Diagram</span>
                </button>
                <button
                  onClick={() => setActiveTab('monitoring')}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                    activeTab === 'monitoring'
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <Monitor size={18} />
                  <span>Live Monitoring</span>
                </button>
              </div>

              {/* Tab Content */}
              {activeTab === 'diagram' ? (
                <>
                  <div className="mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      {systems.find(s => s.id === selectedSystem)?.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Click components for detailed specifications • Animation shows refrigerant flow cycle
                      {showMeasurementTools && " • Interactive measurement tools overlay"}
                    </p>
                  </div>
                  <div className="relative hvac-diagram">
                    <HVACDiagram
                      systemType={selectedSystem}
                      isAnimating={isAnimating}
                      onComponentClick={setSelectedComponent}
                      selectedComponent={selectedComponent}
                    />
                    {showMeasurementTools && (
                      <InteractiveMeasurementTools
                        isAnimating={isAnimating}
                        systemType={selectedSystem}
                        onToolClick={setSelectedTool}
                        selectedTool={selectedTool}
                      />
                    )}
                  </div>
                </>
              ) : (
                <>
                  <div className="mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Real-time System Monitoring
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Live system performance data, alerts, and diagnostic information
                    </p>
                  </div>
                  <SystemMonitoringDashboard
                    isAnimating={isAnimating}
                    systemType={selectedSystem}
                  />
                </>
              )}
            </GlassCard>
          </div>

          {/* Component Information */}
          <div className="lg:col-span-1">
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Component Details
              </h3>
              
              {selectedTool ? (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Activity className="text-purple-600" size={20} />
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        Measurement Tool: {selectedTool.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                      Real-time system monitoring and diagnostic measurement tool providing live operational data.
                    </p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Live Readings:</h5>
                    <div className="space-y-2">
                      <div className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                        {isAnimating ? "Real-time data updating every second" : "Static readings - start animation for live data"}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                        Accuracy: ±1% of reading
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                        Response time: <1 second
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                        Data logging: Continuous monitoring
                      </div>
                    </div>
                  </div>

                  {/* Detail View Button for Measurement Tools */}
                  <div className="mt-4">
                    <button
                      onClick={() => setShowDetailView(true)}
                      className="w-full flex items-center justify-center space-x-2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors duration-200"
                    >
                      <Eye size={16} />
                      <span>View Tool Details</span>
                    </button>
                  </div>
                </div>
              ) : selectedComponent && componentInfo[selectedComponent as keyof typeof componentInfo] ? (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Info className="text-accent-600" size={20} />
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {componentInfo[selectedComponent as keyof typeof componentInfo].title}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                      {componentInfo[selectedComponent as keyof typeof componentInfo].description}
                    </p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Function:</h5>
                    <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                      {componentInfo[selectedComponent as keyof typeof componentInfo].function}
                    </p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-3 text-sm">Technical Specifications:</h5>
                    <div className="space-y-2">
                      {componentInfo[selectedComponent as keyof typeof componentInfo].specifications.map((spec, index) => (
                        <div key={index} className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                          {spec}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Detail View Button for Components */}
                  {(selectedComponent === 'compressor' || selectedComponent === 'evaporator' || selectedComponent === 'condenser') && (
                    <div className="mt-4">
                      <button
                        onClick={() => setShowDetailView(true)}
                        className="w-full flex items-center justify-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
                      >
                        <Eye size={16} />
                        <span>View Cutaway Diagram</span>
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Info size={24} className="text-gray-400" />
                  </div>
                  <p className="text-gray-500 dark:text-gray-400 text-sm leading-relaxed">
                    Select a component in the diagram to view detailed technical specifications
                    and operational parameters, or click on measurement tools for live system data.
                  </p>
                </div>
              )}
            </GlassCard>

            {/* System Parameters */}
            <GlassCard className="p-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Live System Data</h3>
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div className="bg-red-50 dark:bg-red-900/20 p-2 rounded">
                    <div className="flex items-center space-x-1 mb-1">
                      <Thermometer className="text-red-500" size={12} />
                      <span className="font-medium text-red-700 dark:text-red-400">High Side</span>
                    </div>
                    <div className="text-red-600 dark:text-red-300">278 PSI</div>
                    <div className="text-red-600 dark:text-red-300">105°F</div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                    <div className="flex items-center space-x-1 mb-1">
                      <Thermometer className="text-blue-500" size={12} />
                      <span className="font-medium text-blue-700 dark:text-blue-400">Low Side</span>
                    </div>
                    <div className="text-blue-600 dark:text-blue-300">118 PSI</div>
                    <div className="text-blue-600 dark:text-blue-300">40°F</div>
                  </div>
                </div>

                <div className="border-t pt-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Superheat</span>
                    <span className="text-sm font-medium text-green-600">8.2°F</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Subcooling</span>
                    <span className="text-sm font-medium text-blue-600">12.5°F</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Ambient</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">95°F</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Return Air</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">75°F</span>
                  </div>
                </div>

                <div className="border-t pt-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Gauge className="text-green-500" size={16} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Current EER</span>
                    </div>
                    <span className="text-sm font-medium text-green-600">12.8</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Zap className="text-yellow-500" size={16} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Power Draw</span>
                    </div>
                    <span className="text-sm font-medium text-yellow-600">2.85 kW</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Capacity</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">36.5 kBTU/hr</span>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>

      {/* Full Screen Diagram Modal */}
      <FullScreenDiagram
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        systemType={selectedSystem}
        isAnimating={isAnimating}
        onComponentClick={setSelectedComponent}
        selectedComponent={selectedComponent}
        onAnimationToggle={() => setIsAnimating(!isAnimating)}
      />

      {/* Component Detail View Modal */}
      <ComponentDetailView
        component={selectedComponent || ''}
        isOpen={showDetailView}
        onClose={() => setShowDetailView(false)}
      />

      {/* Export & Share Modal */}
      <ExportShareModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        systemType={selectedSystem}
        selectedComponent={selectedComponent}
      />
    </div>
  );
};

export default DiagramsPage;