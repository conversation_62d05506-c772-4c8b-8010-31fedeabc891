import React, { useState } from 'react';
import { Info, Play, Pause, RotateCcw, Thermometer, Gauge, Zap } from 'lucide-react';
import GlassCard from '../components/GlassCard';
import HVACDiagram from '../components/HVACDiagram';

const DiagramsPage = () => {
  const [selectedSystem, setSelectedSystem] = useState('split-system');
  const [isAnimating, setIsAnimating] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);

  const systems = [
    {
      id: 'split-system',
      name: 'Split System AC',
      description: 'Residential cooling system with separate indoor and outdoor units',
      difficulty: 'Beginner',
      components: 4
    },
    {
      id: 'heat-pump',
      name: 'Heat Pump System',
      description: 'Reversible refrigeration system for heating and cooling',
      difficulty: 'Intermediate',
      components: 6
    },
    {
      id: 'packaged-unit',
      name: 'Packaged RTU',
      description: 'Commercial rooftop unit with integrated components',
      difficulty: 'Advanced',
      components: 8
    }
  ];

  const componentInfo = {
    'evaporator': {
      title: 'Evaporator Coil',
      description: 'Heat exchanger that absorbs heat from indoor air, causing liquid refrigerant to evaporate into vapor.',
      specifications: [
        'Operating Pressure: 70-80 PSI (R-410A)',
        'Saturation Temperature: 40-45°F',
        'Superheat: 8-12°F',
        'Material: Copper tubes, aluminum fins',
        'Capacity: 2-5 tons typical residential'
      ],
      function: 'Removes sensible and latent heat from indoor air through refrigerant evaporation process.'
    },
    'condenser': {
      title: 'Condenser Coil',
      description: 'Heat exchanger that rejects heat to outdoor air, causing refrigerant vapor to condense into liquid.',
      specifications: [
        'Operating Pressure: 200-250 PSI (R-410A)',
        'Saturation Temperature: 100-120°F',
        'Subcooling: 10-15°F',
        'Material: Copper tubes, aluminum fins',
        'Heat Rejection: 12,000-60,000 BTU/hr'
      ],
      function: 'Rejects heat absorbed from indoor space plus compressor work to outdoor environment.'
    },
    'compressor': {
      title: 'Compressor',
      description: 'Mechanical device that increases refrigerant pressure and temperature while circulating refrigerant through the system.',
      specifications: [
        'Type: Scroll, reciprocating, or rotary',
        'Power Input: 1-5 HP residential',
        'Voltage: 208-230V single/three phase',
        'Compression Ratio: 2.5-4.0:1',
        'Efficiency: 9-15 EER typical'
      ],
      function: 'Provides pressure differential to circulate refrigerant and adds energy to refrigeration cycle.'
    },
    'expansion-valve': {
      title: 'Expansion Device',
      description: 'Metering device that reduces refrigerant pressure and temperature while controlling refrigerant flow rate.',
      specifications: [
        'Type: TXV, EEV, or fixed orifice',
        'Capacity: Matched to evaporator load',
        'Superheat Control: 8-12°F target',
        'Pressure Drop: 150-180 PSI typical',
        'Response Time: 30-60 seconds (TXV)'
      ],
      function: 'Controls refrigerant flow rate and creates pressure drop for proper evaporator operation.'
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'Intermediate': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'Advanced': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            HVAC System Diagrams
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl">
            Interactive technical diagrams with accurate component specifications, 
            animated refrigerant flow, and detailed system operation analysis.
          </p>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* System Selection */}
          <div className="lg:col-span-1 space-y-6">
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Type</h3>
              <div className="space-y-3">
                {systems.map((system) => (
                  <button
                    key={system.id}
                    onClick={() => {
                      setSelectedSystem(system.id);
                      setSelectedComponent(null);
                      setIsAnimating(false);
                    }}
                    className={`w-full text-left p-4 rounded-lg border transition-all duration-200 ${
                      selectedSystem === system.id
                        ? 'border-accent-600 bg-accent-50 dark:bg-accent-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <div className="font-semibold text-gray-900 dark:text-white mb-1">{system.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">{system.description}</div>
                    <div className="flex items-center justify-between">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getDifficultyColor(system.difficulty)}`}>
                        {system.difficulty}
                      </span>
                      <span className="text-xs text-gray-500">{system.components} components</span>
                    </div>
                  </button>
                ))}
              </div>
            </GlassCard>

            {/* Controls */}
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Animation Controls</h3>
              <div className="space-y-3">
                <button
                  onClick={() => setIsAnimating(!isAnimating)}
                  className="w-full flex items-center justify-center space-x-2 bg-accent-600 text-white px-4 py-3 rounded-lg hover:bg-accent-700 transition-colors duration-200"
                >
                  {isAnimating ? <Pause size={18} /> : <Play size={18} />}
                  <span>{isAnimating ? 'Pause Flow' : 'Start Flow'}</span>
                </button>
                <button
                  onClick={() => {
                    setIsAnimating(false);
                    setSelectedComponent(null);
                  }}
                  className="w-full flex items-center justify-center space-x-2 bg-gray-600 text-white px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-200"
                >
                  <RotateCcw size={18} />
                  <span>Reset View</span>
                </button>
              </div>
            </GlassCard>

            {/* Legend */}
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Legend</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">High pressure, hot gas</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">High pressure, warm liquid</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">Low pressure, cold liquid</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-cyan-500 rounded-full"></div>
                  <span className="text-gray-700 dark:text-gray-300">Low pressure, cool vapor</span>
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Main Diagram */}
          <div className="lg:col-span-2">
            <GlassCard className="p-6">
              <div className="mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {systems.find(s => s.id === selectedSystem)?.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Click components for detailed specifications • Animation shows refrigerant flow cycle
                </p>
              </div>
              <HVACDiagram 
                systemType={selectedSystem}
                isAnimating={isAnimating}
                onComponentClick={setSelectedComponent}
                selectedComponent={selectedComponent}
              />
            </GlassCard>
          </div>

          {/* Component Information */}
          <div className="lg:col-span-1">
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Component Details
              </h3>
              
              {selectedComponent && componentInfo[selectedComponent as keyof typeof componentInfo] ? (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Info className="text-accent-600" size={20} />
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {componentInfo[selectedComponent as keyof typeof componentInfo].title}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                      {componentInfo[selectedComponent as keyof typeof componentInfo].description}
                    </p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2 text-sm">Function:</h5>
                    <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                      {componentInfo[selectedComponent as keyof typeof componentInfo].function}
                    </p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white mb-3 text-sm">Technical Specifications:</h5>
                    <div className="space-y-2">
                      {componentInfo[selectedComponent as keyof typeof componentInfo].specifications.map((spec, index) => (
                        <div key={index} className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-lg px-3 py-2">
                          {spec}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Info size={24} className="text-gray-400" />
                  </div>
                  <p className="text-gray-500 dark:text-gray-400 text-sm leading-relaxed">
                    Select a component in the diagram to view detailed technical specifications 
                    and operational parameters.
                  </p>
                </div>
              )}
            </GlassCard>

            {/* System Parameters */}
            <GlassCard className="p-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Parameters</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Thermometer className="text-red-500" size={16} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Outdoor Temp</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">95°F</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Thermometer className="text-blue-500" size={16} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Indoor Temp</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">75°F</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Gauge className="text-green-500" size={16} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">System EER</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">12.5</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Zap className="text-yellow-500" size={16} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Power Draw</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">3.2 kW</span>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DiagramsPage;