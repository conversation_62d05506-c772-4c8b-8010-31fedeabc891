import React from 'react';
import { X, Zap, Thermometer, Gauge, Settings } from 'lucide-react';

interface ComponentDetailViewProps {
  component: string;
  isOpen: boolean;
  onClose: () => void;
}

const ComponentDetailView = ({ component, isOpen, onClose }: ComponentDetailViewProps) => {
  if (!isOpen || !component) return null;

  const getComponentDetails = (comp: string) => {
    switch (comp) {
      case 'compressor':
        return {
          title: 'Variable Speed Scroll Compressor',
          description: 'Hermetic scroll compressor with inverter drive technology for variable capacity control',
          cutawayView: (
            <svg viewBox="0 0 400 300" className="w-full h-64 bg-gray-50 rounded-lg border">
              {/* Compressor Shell */}
              <ellipse cx="200" cy="150" rx="120" ry="100" fill="#e5e7eb" stroke="#374151" strokeWidth="2"/>
              <ellipse cx="200" cy="140" rx="110" ry="90" fill="#f3f4f6" stroke="#6b7280" strokeWidth="1"/>
              
              {/* Scroll Elements */}
              <g className="scroll-elements">
                <path d="M150,120 Q200,100 250,120 Q200,140 150,120" fill="#3b82f6" stroke="#1d4ed8" strokeWidth="2"/>
                <path d="M160,140 Q200,120 240,140 Q200,160 160,140" fill="#60a5fa" stroke="#2563eb" strokeWidth="2"/>
                <circle cx="200" cy="130" r="8" fill="#1e40af"/>
              </g>
              
              {/* Motor */}
              <rect x="170" y="180" width="60" height="40" fill="#fbbf24" stroke="#f59e0b" strokeWidth="2" rx="4"/>
              <text x="200" y="202" textAnchor="middle" className="text-xs font-bold fill-gray-800">MOTOR</text>
              
              {/* Suction Port */}
              <circle cx="120" cy="120" r="15" fill="#0891b2" stroke="#0e7490" strokeWidth="2"/>
              <text x="120" y="100" textAnchor="middle" className="text-xs font-bold fill-cyan-700">SUCTION</text>
              
              {/* Discharge Port */}
              <circle cx="280" cy="120" r="15" fill="#dc2626" stroke="#b91c1c" strokeWidth="2"/>
              <text x="280" y="100" textAnchor="middle" className="text-xs font-bold fill-red-700">DISCHARGE</text>
              
              {/* Electrical Connections */}
              <rect x="220" y="80" width="40" height="20" fill="#374151" stroke="#1f2937" strokeWidth="1" rx="2"/>
              <text x="240" y="92" textAnchor="middle" className="text-xs fill-white">VFD</text>
              
              {/* Labels */}
              <text x="200" y="280" textAnchor="middle" className="text-sm font-bold fill-gray-800">
                Scroll Compressor Cutaway View
              </text>
            </svg>
          ),
          specifications: [
            'Type: Hermetic scroll with inverter drive',
            'Capacity: Variable 40-110% modulation',
            'Displacement: 2.8 cubic inches per revolution',
            'Motor: Permanent magnet, variable speed',
            'Refrigerant: R-410A compatible',
            'Sound Level: 72 dB @ 10 feet',
            'Efficiency: 95% volumetric efficiency',
            'Operating Range: -10°F to 130°F ambient'
          ],
          operatingPrinciple: 'Two spiral-shaped scrolls create compression chambers that progressively reduce in volume as refrigerant moves from outer edge to center, providing smooth, efficient compression with minimal vibration.'
        };
        
      case 'evaporator':
        return {
          title: 'A-Frame Evaporator Coil',
          description: 'Microchannel aluminum evaporator with enhanced heat transfer surface and integrated condensate management',
          cutawayView: (
            <svg viewBox="0 0 400 300" className="w-full h-64 bg-gray-50 rounded-lg border">
              {/* A-Frame Structure */}
              <path d="M100,200 L200,100 L300,200 L250,200 L200,140 L150,200 Z" 
                    fill="#dbeafe" stroke="#2563eb" strokeWidth="2"/>
              <path d="M150,200 L200,140 L250,200" 
                    fill="#93c5fd" stroke="#3b82f6" strokeWidth="1"/>
              
              {/* Refrigerant Tubes */}
              {Array.from({length: 8}, (_, i) => (
                <g key={i}>
                  <line x1={120 + i * 20} y1={180 - i * 10} x2={120 + i * 20} y2={200} 
                        stroke="#1d4ed8" strokeWidth="3"/>
                  <line x1={200 + i * 10} y1={140 + i * 6} x2={200 + i * 10} y2={200} 
                        stroke="#1d4ed8" strokeWidth="3"/>
                </g>
              ))}
              
              {/* Fins */}
              {Array.from({length: 15}, (_, i) => (
                <line key={i} x1={110 + i * 12} y1={190 - i * 6} x2={110 + i * 12} y2={200} 
                      stroke="#60a5fa" strokeWidth="1" opacity="0.7"/>
              ))}
              
              {/* Condensate Drain Pan */}
              <rect x="120" y="200" width="160" height="15" fill="#64748b" stroke="#475569" strokeWidth="1" rx="3"/>
              <circle cx="250" cy="207" r="3" fill="#374151"/>
              <text x="200" y="225" textAnchor="middle" className="text-xs fill-gray-600">Condensate Drain</text>
              
              {/* Air Flow Arrows */}
              <path d="M50,150 L90,150" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrow)"/>
              <path d="M310,150 L350,150" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrow)"/>
              
              <text x="70" y="140" className="text-xs fill-green-600">Warm Air</text>
              <text x="330" y="140" className="text-xs fill-blue-600">Cool Air</text>
              
              <text x="200" y="280" textAnchor="middle" className="text-sm font-bold fill-gray-800">
                A-Frame Evaporator Cross-Section
              </text>
              
              <defs>
                <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#10b981"/>
                </marker>
              </defs>
            </svg>
          ),
          specifications: [
            'Configuration: A-frame, 2-row deep',
            'Material: Aluminum microchannel tubes',
            'Fin Density: 14 fins per inch',
            'Face Area: 3.5 square feet',
            'Refrigerant Circuiting: 4 circuits',
            'Pressure Drop: 0.3" WC air side',
            'Condensate Capacity: 8 pints/hour',
            'Operating Temperature: 32°F to 50°F'
          ],
          operatingPrinciple: 'Refrigerant evaporates at constant pressure and temperature while absorbing heat from air passing over the coil surface. The A-frame design maximizes heat transfer surface area while maintaining compact footprint.'
        };
        
      case 'condenser':
        return {
          title: 'Microchannel Condenser Coil',
          description: 'All-aluminum microchannel condenser with optimized fin geometry for maximum heat rejection efficiency',
          cutawayView: (
            <svg viewBox="0 0 400 300" className="w-full h-64 bg-gray-50 rounded-lg border">
              {/* Condenser Frame */}
              <rect x="80" y="120" width="240" height="80" fill="#fef3c7" stroke="#d97706" strokeWidth="2" rx="4"/>
              
              {/* Microchannel Tubes */}
              {Array.from({length: 12}, (_, i) => (
                <rect key={i} x={90 + i * 18} y="130" width="12" height="60" 
                      fill="#f59e0b" stroke="#d97706" strokeWidth="1" rx="1"/>
              ))}
              
              {/* Fins */}
              {Array.from({length: 20}, (_, i) => (
                <line key={i} x1="85" y1={135 + i * 3} x2="315" y2={135 + i * 3} 
                      stroke="#fbbf24" strokeWidth="0.5" opacity="0.8"/>
              ))}
              
              {/* Headers */}
              <rect x="70" y="115" width="260" height="10" fill="#dc2626" stroke="#b91c1c" strokeWidth="2" rx="2"/>
              <rect x="70" y="195" width="260" height="10" fill="#dc2626" stroke="#b91c1c" strokeWidth="2" rx="2"/>
              
              {/* Refrigerant Flow */}
              <circle cx="90" cy="120" r="4" fill="#ef4444"/>
              <circle cx="310" cy="200" r="4" fill="#f97316"/>
              <text x="90" y="110" textAnchor="middle" className="text-xs fill-red-600">Hot Gas In</text>
              <text x="310" y="220" textAnchor="middle" className="text-xs fill-orange-600">Liquid Out</text>
              
              {/* Air Flow */}
              <path d="M50,160 L70,160" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrow)"/>
              <path d="M330,160 L350,160" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrow)"/>
              
              <text x="60" y="150" className="text-xs fill-green-600">95°F Air</text>
              <text x="340" y="150" className="text-xs fill-red-600">110°F Air</text>
              
              <text x="200" y="280" textAnchor="middle" className="text-sm font-bold fill-gray-800">
                Microchannel Condenser Detail
              </text>
            </svg>
          ),
          specifications: [
            'Type: All-aluminum microchannel',
            'Configuration: Single row, parallel flow',
            'Tube Diameter: 16mm x 1.5mm',
            'Fin Type: Louvered, enhanced',
            'Face Area: 8.2 square feet',
            'Refrigerant Charge: 40% less than copper',
            'Corrosion Resistance: Marine grade coating',
            'Heat Rejection: 42,000 BTU/hr @ ARI'
          ],
          operatingPrinciple: 'High-pressure refrigerant vapor enters the header and flows through parallel microchannels where it condenses to liquid while rejecting heat to ambient air flowing across the enhanced fin surface.'
        };
        
      default:
        return null;
    }
  };

  const details = getComponentDetails(component);
  if (!details) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{details.title}</h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">{details.description}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X size={24} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Cutaway View */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Settings className="mr-2" size={20} />
              Internal Structure
            </h3>
            {details.cutawayView}
          </div>

          {/* Operating Principle */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
              <Zap className="mr-2" size={20} />
              Operating Principle
            </h3>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              {details.operatingPrinciple}
            </p>
          </div>

          {/* Technical Specifications */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
              <Gauge className="mr-2" size={20} />
              Technical Specifications
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {details.specifications.map((spec, index) => (
                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{spec}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComponentDetailView;
