import React, { useEffect, useState } from 'react';

interface HeatPumpDiagramProps {
  systemType: string;
  isAnimating: boolean;
  onComponentClick: (component: string) => void;
  selectedComponent: string | null;
}

const HeatPumpDiagram = ({ systemType, isAnimating, onComponentClick, selectedComponent }: HeatPumpDiagramProps) => {
  const [animationStep, setAnimationStep] = useState(0);
  const [isHeatingMode, setIsHeatingMode] = useState(false);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isAnimating) {
      interval = setInterval(() => {
        setAnimationStep((prev) => (prev + 1) % 4);
      }, 1500);
    }
    return () => clearInterval(interval);
  }, [isAnimating]);

  const getComponentClass = (component: string) => {
    const baseClass = "cursor-pointer transition-all duration-200 hover:opacity-80";
    const selectedClass = selectedComponent === component ? "drop-shadow-lg" : "";
    return `${baseClass} ${selectedClass}`;
  };

  const getFlowOpacity = (step: number) => {
    if (!isAnimating) return 0.4;
    return animationStep === step ? 1 : 0.3;
  };

  return (
    <div className="w-full max-w-5xl mx-auto">
      <svg viewBox="0 0 900 650" className="w-full h-auto bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
        {/* Definitions */}
        <defs>
          {/* Enhanced Grid Pattern */}
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f1f5f9" strokeWidth="0.5" opacity="0.3"/>
            <circle cx="0" cy="0" r="0.5" fill="#e2e8f0" opacity="0.4"/>
          </pattern>
          
          {/* Heat Pump Flow Gradients */}
          <linearGradient id="hotGasFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#dc2626" stopOpacity="1"/>
            <stop offset="100%" stopColor="#f97316" stopOpacity="0.8"/>
          </linearGradient>
          
          <linearGradient id="warmLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#ea580c" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#d97706" stopOpacity="0.8"/>
          </linearGradient>
          
          <linearGradient id="coldLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#1d4ed8" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.7"/>
          </linearGradient>
          
          <linearGradient id="coolVaporFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#0891b2" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#0e7490" stopOpacity="0.8"/>
          </linearGradient>

          {/* 3D Component Gradients */}
          <linearGradient id="metalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f8fafc"/>
            <stop offset="100%" stopColor="#94a3b8"/>
          </linearGradient>
          
          <linearGradient id="compressorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6"/>
            <stop offset="100%" stopColor="#1e40af"/>
          </linearGradient>

          {/* Filters */}
          <filter id="highlight" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix values="0 0 0 0 0.23 0 0 0 0 0.51 0 0 0 0 0.96 0 0 0 1 0"/>
            <feOffset dx="0" dy="0" result="colored-blur"/>
            <feMerge>
              <feMergeNode in="colored-blur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          
          <filter id="shadow3d" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#000000" floodOpacity="0.3"/>
          </filter>

          {/* Arrow markers */}
          <marker id="flowArrow" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto">
            <polygon points="0 0, 12 5, 0 10, 3 5" fill="url(#metalGradient)" stroke="#475569" strokeWidth="1"/>
          </marker>
        </defs>
        
        {/* Background */}
        <rect width="900" height="650" fill="url(#grid)"/>

        {/* Title */}
        <text x="450" y="30" textAnchor="middle" className="text-lg font-bold fill-gray-800 dark:fill-gray-200">
          Heat Pump System - Reversible Refrigeration Cycle
        </text>
        <text x="450" y="50" textAnchor="middle" className="text-sm fill-gray-600 dark:fill-gray-400">
          3-Ton Variable Speed Heat Pump • R-410A • Heating/Cooling Mode
        </text>

        {/* Mode Toggle */}
        <g className="mode-toggle">
          <rect x="350" y="60" width="200" height="30" fill="#f3f4f6" stroke="#d1d5db" strokeWidth="1" rx="15"/>
          <rect 
            x={isHeatingMode ? "455" : "355"} 
            y="65" 
            width="90" 
            height="20" 
            fill={isHeatingMode ? "#dc2626" : "#2563eb"} 
            rx="10"
            className="cursor-pointer transition-all duration-300"
            onClick={() => setIsHeatingMode(!isHeatingMode)}
          />
          <text x="400" y="78" textAnchor="middle" className="text-xs font-medium fill-white">
            {isHeatingMode ? "HEATING" : "COOLING"}
          </text>
        </g>

        {/* Outdoor Unit */}
        <g className="outdoor-unit">
          <rect 
            x="80" y="120" width="240" height="200" 
            fill="url(#metalGradient)" stroke="#334155" strokeWidth="3" 
            rx="8" filter="url(#shadow3d)"
          />
          
          <text x="200" y="110" textAnchor="middle" className="text-sm font-bold fill-gray-700">
            OUTDOOR UNIT
          </text>
          
          {/* Outdoor Coil (Condenser in Cooling, Evaporator in Heating) */}
          <rect 
            x="100" y="140" width="200" height="50" 
            fill={isHeatingMode ? "#dbeafe" : "#fef3c7"} 
            stroke={isHeatingMode ? "#2563eb" : "#d97706"} 
            strokeWidth="2" 
            rx="4"
            className={getComponentClass('outdoor-coil')}
            onClick={() => onComponentClick('outdoor-coil')}
            filter={selectedComponent === 'outdoor-coil' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <text x="200" y="170" textAnchor="middle" className="text-sm font-medium fill-gray-800">
            {isHeatingMode ? "Evaporator Coil" : "Condenser Coil"}
          </text>
          <text x="200" y="185" textAnchor="middle" className="text-xs fill-gray-600">
            {isHeatingMode ? "Heat Absorption" : "Heat Rejection"}
          </text>
          
          {/* Compressor */}
          <ellipse 
            cx="200" cy="250" rx="32" ry="28" 
            fill="url(#compressorGradient)" stroke="#1e3a8a" strokeWidth="3"
            className={getComponentClass('compressor')}
            onClick={() => onComponentClick('compressor')}
            filter={selectedComponent === 'compressor' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <text x="200" y="255" textAnchor="middle" className="text-sm font-medium fill-white">
            Compressor
          </text>
          
          {/* Reversing Valve */}
          <rect 
            x="180" y="290" width="40" height="20" 
            fill="#f59e0b" stroke="#d97706" strokeWidth="2" 
            rx="4"
            className={getComponentClass('reversing-valve')}
            onClick={() => onComponentClick('reversing-valve')}
            filter={selectedComponent === 'reversing-valve' ? 'url(#highlight)' : ''}
          />
          <text x="200" y="302" textAnchor="middle" className="text-xs font-medium fill-white">
            4-Way Valve
          </text>
        </g>

        {/* Indoor Unit */}
        <g className="indoor-unit">
          <rect 
            x="580" y="200" width="240" height="120" 
            fill="url(#metalGradient)" stroke="#334155" strokeWidth="3" 
            rx="8" filter="url(#shadow3d)"
          />
          
          <text x="700" y="190" textAnchor="middle" className="text-sm font-bold fill-gray-700">
            INDOOR UNIT
          </text>
          
          {/* Indoor Coil (Evaporator in Cooling, Condenser in Heating) */}
          <rect 
            x="600" y="220" width="200" height="40" 
            fill={isHeatingMode ? "#fef3c7" : "#dbeafe"} 
            stroke={isHeatingMode ? "#d97706" : "#2563eb"} 
            strokeWidth="2" 
            rx="4"
            className={getComponentClass('indoor-coil')}
            onClick={() => onComponentClick('indoor-coil')}
            filter={selectedComponent === 'indoor-coil' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <text x="700" y="245" textAnchor="middle" className="text-sm font-medium fill-gray-800">
            {isHeatingMode ? "Condenser Coil" : "Evaporator Coil"}
          </text>
          <text x="700" y="255" textAnchor="middle" className="text-xs fill-gray-600">
            {isHeatingMode ? "Heat Rejection" : "Heat Absorption"}
          </text>
          
          {/* Blower */}
          <circle 
            cx="700" cy="285" r="18" 
            fill="url(#metalGradient)" stroke="#475569" strokeWidth="2" 
            filter="url(#shadow3d)"
          />
          <text x="700" y="340" textAnchor="middle" className="text-xs fill-gray-600">
            Variable Speed Blower
          </text>
        </g>

        {/* Refrigerant Lines */}
        <g className="refrigerant-lines">
          {/* Lines change direction based on mode */}
          <path 
            d="M200,220 L200,190" 
            fill="none" 
            stroke="url(#hotGasFlow)" 
            strokeWidth="8" 
            opacity={getFlowOpacity(0)}
            markerEnd="url(#flowArrow)"
          />
          
          <path 
            d="M300,165 Q450,165 580,240" 
            fill="none" 
            stroke={isHeatingMode ? "url(#hotGasFlow)" : "url(#warmLiquidFlow)"} 
            strokeWidth="8" 
            opacity={getFlowOpacity(1)}
            markerEnd="url(#flowArrow)"
          />
          
          <path 
            d="M600,260 Q450,320 300,260 Q250,260 200,280" 
            fill="none" 
            stroke={isHeatingMode ? "url(#coolVaporFlow)" : "url(#coolVaporFlow)"} 
            strokeWidth="8" 
            opacity={getFlowOpacity(3)}
            markerEnd="url(#flowArrow)"
          />
        </g>

        {/* System Performance Data */}
        <g className="performance-data">
          <rect x="650" y="380" width="200" height="120" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1" rx="4"/>
          <text x="750" y="400" textAnchor="middle" className="text-sm font-semibold fill-gray-800">
            {isHeatingMode ? "Heating Performance" : "Cooling Performance"}
          </text>
          
          <text x="660" y="420" className="text-xs fill-gray-700">
            Capacity: {isHeatingMode ? "38,000" : "36,000"} BTU/hr
          </text>
          <text x="660" y="435" className="text-xs fill-gray-700">
            Power: {isHeatingMode ? "3.1" : "2.9"} kW
          </text>
          <text x="660" y="450" className="text-xs fill-gray-700">
            {isHeatingMode ? "HSPF: 10.5" : "SEER: 16.0"}
          </text>
          <text x="660" y="465" className="text-xs fill-gray-700">
            {isHeatingMode ? "COP: 3.4" : "EER: 12.4"}
          </text>
          <text x="660" y="480" className="text-xs fill-gray-700">Refrigerant: R-410A (4.2 lbs)</text>
        </g>
      </svg>
    </div>
  );
};

export default HeatPumpDiagram;
