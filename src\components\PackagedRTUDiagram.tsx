import React, { useEffect, useState } from 'react';

interface PackagedRTUDiagramProps {
  systemType: string;
  isAnimating: boolean;
  onComponentClick: (component: string) => void;
  selectedComponent: string | null;
}

const PackagedRTUDiagram = ({ systemType, isAnimating, onComponentClick, selectedComponent }: PackagedRTUDiagramProps) => {
  const [animationStep, setAnimationStep] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isAnimating) {
      interval = setInterval(() => {
        setAnimationStep((prev) => (prev + 1) % 4);
      }, 1500);
    }
    return () => clearInterval(interval);
  }, [isAnimating]);

  const getComponentClass = (component: string) => {
    const baseClass = "cursor-pointer transition-all duration-200 hover:opacity-80";
    const selectedClass = selectedComponent === component ? "drop-shadow-lg" : "";
    return `${baseClass} ${selectedClass}`;
  };

  const getFlowOpacity = (step: number) => {
    if (!isAnimating) return 0.4;
    return animationStep === step ? 1 : 0.3;
  };

  return (
    <div className="w-full max-w-5xl mx-auto">
      <svg viewBox="0 0 900 650" className="w-full h-auto bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
        {/* Definitions */}
        <defs>
          {/* Grid Pattern */}
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f1f5f9" strokeWidth="0.5" opacity="0.3"/>
          </pattern>
          
          {/* Flow Gradients */}
          <linearGradient id="hotGasFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#dc2626" stopOpacity="1"/>
            <stop offset="100%" stopColor="#f97316" stopOpacity="0.8"/>
          </linearGradient>
          
          <linearGradient id="warmLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#ea580c" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#d97706" stopOpacity="0.8"/>
          </linearGradient>
          
          <linearGradient id="coldLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#1d4ed8" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.7"/>
          </linearGradient>
          
          <linearGradient id="coolVaporFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#0891b2" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#0e7490" stopOpacity="0.8"/>
          </linearGradient>

          {/* 3D Gradients */}
          <linearGradient id="metalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f8fafc"/>
            <stop offset="100%" stopColor="#94a3b8"/>
          </linearGradient>
          
          <linearGradient id="compressorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6"/>
            <stop offset="100%" stopColor="#1e40af"/>
          </linearGradient>

          {/* Filters */}
          <filter id="highlight" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix values="0 0 0 0 0.23 0 0 0 0 0.51 0 0 0 0 0.96 0 0 0 1 0"/>
            <feOffset dx="0" dy="0" result="colored-blur"/>
            <feMerge>
              <feMergeNode in="colored-blur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          
          <filter id="shadow3d" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#000000" floodOpacity="0.3"/>
          </filter>

          {/* Arrow markers */}
          <marker id="flowArrow" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto">
            <polygon points="0 0, 12 5, 0 10, 3 5" fill="url(#metalGradient)" stroke="#475569" strokeWidth="1"/>
          </marker>
        </defs>
        
        {/* Background */}
        <rect width="900" height="650" fill="url(#grid)"/>

        {/* Title */}
        <text x="450" y="30" textAnchor="middle" className="text-lg font-bold fill-gray-800 dark:fill-gray-200">
          Packaged Rooftop Unit (RTU) - Commercial HVAC System
        </text>
        <text x="450" y="50" textAnchor="middle" className="text-sm fill-gray-600 dark:fill-gray-400">
          10-Ton Commercial RTU • R-410A • Gas Heat/Electric Cool • Economizer
        </text>

        {/* Main RTU Housing */}
        <g className="rtu-housing">
          <rect 
            x="150" y="150" width="600" height="300" 
            fill="url(#metalGradient)" stroke="#334155" strokeWidth="4" 
            rx="12" filter="url(#shadow3d)"
          />
          
          <text x="450" y="135" textAnchor="middle" className="text-lg font-bold fill-gray-700">
            PACKAGED ROOFTOP UNIT
          </text>
          <text x="450" y="125" textAnchor="middle" className="text-sm fill-gray-600">
            Model: RTU-100 • 10 Tons • 120,000 BTU/hr
          </text>
        </g>

        {/* Condenser Section */}
        <g className="condenser-section">
          <rect x="170" y="170" width="180" height="120" fill="#fef3c7" stroke="#d97706" strokeWidth="2" rx="6"/>
          <text x="260" y="190" textAnchor="middle" className="text-sm font-bold fill-gray-800">
            CONDENSER SECTION
          </text>
          
          {/* Condenser Coil */}
          <rect 
            x="180" y="200" width="160" height="40" 
            fill="#f59e0b" stroke="#d97706" strokeWidth="2" 
            rx="4"
            className={getComponentClass('condenser')}
            onClick={() => onComponentClick('condenser')}
            filter={selectedComponent === 'condenser' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <text x="260" y="225" textAnchor="middle" className="text-xs font-medium fill-white">
            Condenser Coil
          </text>
          
          {/* Compressor */}
          <ellipse 
            cx="260" cy="260" rx="25" ry="20" 
            fill="url(#compressorGradient)" stroke="#1e3a8a" strokeWidth="2"
            className={getComponentClass('compressor')}
            onClick={() => onComponentClick('compressor')}
            filter={selectedComponent === 'compressor' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <text x="260" y="265" textAnchor="middle" className="text-xs font-medium fill-white">
            Scroll Comp
          </text>
        </g>

        {/* Evaporator Section */}
        <g className="evaporator-section">
          <rect x="370" y="170" width="180" height="120" fill="#dbeafe" stroke="#2563eb" strokeWidth="2" rx="6"/>
          <text x="460" y="190" textAnchor="middle" className="text-sm font-bold fill-gray-800">
            EVAPORATOR SECTION
          </text>
          
          {/* Evaporator Coil */}
          <rect 
            x="380" y="200" width="160" height="40" 
            fill="#3b82f6" stroke="#2563eb" strokeWidth="2" 
            rx="4"
            className={getComponentClass('evaporator')}
            onClick={() => onComponentClick('evaporator')}
            filter={selectedComponent === 'evaporator' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <text x="460" y="225" textAnchor="middle" className="text-xs font-medium fill-white">
            Evaporator Coil
          </text>
          
          {/* Supply Fan */}
          <circle 
            cx="460" cy="260" r="18" 
            fill="url(#metalGradient)" stroke="#475569" strokeWidth="2" 
            filter="url(#shadow3d)"
          />
          <text x="460" y="265" textAnchor="middle" className="text-xs font-medium fill-gray-800">
            Fan
          </text>
        </g>

        {/* Gas Heat Section */}
        <g className="gas-heat-section">
          <rect x="570" y="170" width="160" height="120" fill="#fef2f2" stroke="#dc2626" strokeWidth="2" rx="6"/>
          <text x="650" y="190" textAnchor="middle" className="text-sm font-bold fill-gray-800">
            GAS HEAT SECTION
          </text>
          
          {/* Heat Exchanger */}
          <rect 
            x="580" y="200" width="140" height="40" 
            fill="#dc2626" stroke="#b91c1c" strokeWidth="2" 
            rx="4"
            className={getComponentClass('heat-exchanger')}
            onClick={() => onComponentClick('heat-exchanger')}
            filter={selectedComponent === 'heat-exchanger' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <text x="650" y="225" textAnchor="middle" className="text-xs font-medium fill-white">
            Heat Exchanger
          </text>
          
          {/* Gas Valve */}
          <rect x="630" y="250" width="40" height="20" fill="#f59e0b" stroke="#d97706" strokeWidth="1" rx="3"/>
          <text x="650" y="262" textAnchor="middle" className="text-xs fill-white">Gas Valve</text>
        </g>

        {/* Economizer Section */}
        <g className="economizer-section">
          <rect x="170" y="310" width="560" height="60" fill="#f0fdf4" stroke="#16a34a" strokeWidth="2" rx="6"/>
          <text x="450" y="330" textAnchor="middle" className="text-sm font-bold fill-gray-800">
            ECONOMIZER SECTION
          </text>
          
          {/* Dampers */}
          <rect 
            x="200" y="340" width="80" height="20" 
            fill="#22c55e" stroke="#16a34a" strokeWidth="1" 
            rx="3"
            className={getComponentClass('outside-air-damper')}
            onClick={() => onComponentClick('outside-air-damper')}
          />
          <text x="240" y="352" textAnchor="middle" className="text-xs fill-white">OA Damper</text>
          
          <rect 
            x="300" y="340" width="80" height="20" 
            fill="#22c55e" stroke="#16a34a" strokeWidth="1" 
            rx="3"
            className={getComponentClass('return-air-damper')}
            onClick={() => onComponentClick('return-air-damper')}
          />
          <text x="340" y="352" textAnchor="middle" className="text-xs fill-white">RA Damper</text>
          
          <rect 
            x="620" y="340" width="80" height="20" 
            fill="#22c55e" stroke="#16a34a" strokeWidth="1" 
            rx="3"
            className={getComponentClass('exhaust-damper')}
            onClick={() => onComponentClick('exhaust-damper')}
          />
          <text x="660" y="352" textAnchor="middle" className="text-xs fill-white">Exhaust</text>
        </g>

        {/* Refrigerant Lines */}
        <g className="refrigerant-lines">
          {/* Discharge Line */}
          <path 
            d="M285,260 Q320,260 380,220" 
            fill="none" 
            stroke="url(#hotGasFlow)" 
            strokeWidth="6" 
            opacity={getFlowOpacity(0)}
            markerEnd="url(#flowArrow)"
          />
          
          {/* Liquid Line */}
          <path 
            d="M340,200 Q340,180 380,200" 
            fill="none" 
            stroke="url(#warmLiquidFlow)" 
            strokeWidth="6" 
            opacity={getFlowOpacity(1)}
            markerEnd="url(#flowArrow)"
          />
          
          {/* Suction Line */}
          <path 
            d="M380,240 Q320,280 285,260" 
            fill="none" 
            stroke="url(#coolVaporFlow)" 
            strokeWidth="6" 
            opacity={getFlowOpacity(3)}
            markerEnd="url(#flowArrow)"
          />
        </g>

        {/* Air Flow Indicators */}
        <g className="air-flow">
          {/* Outside Air */}
          <path d="M150,350 L200,350" stroke="#22c55e" strokeWidth="3" markerEnd="url(#flowArrow)"/>
          <text x="175" y="340" textAnchor="middle" className="text-xs fill-green-600">Outside Air</text>
          
          {/* Supply Air */}
          <path d="M460,290 L460,320" stroke="#3b82f6" strokeWidth="3" markerEnd="url(#flowArrow)"/>
          <text x="460" y="310" textAnchor="middle" className="text-xs fill-blue-600">Supply</text>
          
          {/* Return Air */}
          <path d="M340,370 L340,340" stroke="#ef4444" strokeWidth="3" markerEnd="url(#flowArrow)"/>
          <text x="340" y="385" textAnchor="middle" className="text-xs fill-red-600">Return</text>
          
          {/* Exhaust Air */}
          <path d="M700,350 L750,350" stroke="#6b7280" strokeWidth="3" markerEnd="url(#flowArrow)"/>
          <text x="725" y="340" textAnchor="middle" className="text-xs fill-gray-600">Exhaust</text>
        </g>

        {/* System Performance Data */}
        <g className="performance-data">
          <rect x="50" y="480" width="800" height="120" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1" rx="4"/>
          <text x="450" y="500" textAnchor="middle" className="text-lg font-semibold fill-gray-800">
            Commercial RTU Performance Data
          </text>
          
          <g className="performance-columns">
            <text x="100" y="520" className="text-sm font-medium fill-gray-700">Cooling Mode:</text>
            <text x="100" y="535" className="text-xs fill-gray-600">Capacity: 120,000 BTU/hr</text>
            <text x="100" y="550" className="text-xs fill-gray-600">Power: 12.5 kW</text>
            <text x="100" y="565" className="text-xs fill-gray-600">EER: 9.6</text>
            <text x="100" y="580" className="text-xs fill-gray-600">Airflow: 4,000 CFM</text>
            
            <text x="300" y="520" className="text-sm font-medium fill-gray-700">Heating Mode:</text>
            <text x="300" y="535" className="text-xs fill-gray-600">Input: 200,000 BTU/hr</text>
            <text x="300" y="550" className="text-xs fill-gray-600">Output: 180,000 BTU/hr</text>
            <text x="300" y="565" className="text-xs fill-gray-600">Efficiency: 90% AFUE</text>
            <text x="300" y="580" className="text-xs fill-gray-600">Gas Consumption: 2.0 therms/hr</text>
            
            <text x="550" y="520" className="text-sm font-medium fill-gray-700">Economizer:</text>
            <text x="550" y="535" className="text-xs fill-gray-600">Free Cooling: 0-100%</text>
            <text x="550" y="550" className="text-xs fill-gray-600">OA Range: 10-100%</text>
            <text x="550" y="565" className="text-xs fill-gray-600">Enthalpy Control</text>
            <text x="550" y="580" className="text-xs fill-gray-600">Energy Savings: 30-50%</text>
            
            <text x="750" y="520" className="text-sm font-medium fill-gray-700">Controls:</text>
            <text x="750" y="535" className="text-xs fill-gray-600">BACnet/IP</text>
            <text x="750" y="550" className="text-xs fill-gray-600">Variable Speed Drives</text>
            <text x="750" y="565" className="text-xs fill-gray-600">Demand Control Vent</text>
            <text x="750" y="580" className="text-xs fill-gray-600">Fault Detection</text>
          </g>
        </g>
      </svg>
    </div>
  );
};

export default PackagedRTUDiagram;
