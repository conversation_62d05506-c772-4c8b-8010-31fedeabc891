import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import HomePage from './pages/HomePage';
import DiagramsPage from './pages/DiagramsPage';
import LessonsPage from './pages/LessonsPage';
import QuizPage from './pages/QuizPage';
import { ThemeProvider } from './contexts/ThemeContext';
import { ProgressProvider } from './contexts/ProgressContext';

function App() {
  return (
    <ThemeProvider>
      <ProgressProvider>
        <Router>
          <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
            <Navbar />
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/diagrams" element={<DiagramsPage />} />
              <Route path="/lessons" element={<LessonsPage />} />
              <Route path="/quiz" element={<QuizPage />} />
            </Routes>
          </div>
        </Router>
      </ProgressProvider>
    </ThemeProvider>
  );
}

export default App;