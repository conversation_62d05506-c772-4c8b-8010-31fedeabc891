import React, { useState } from 'react';
import { Check<PERSON>ircle, XCircle, ArrowRight, RefreshCw, Award, Target, Clock } from 'lucide-react';
import GlassCard from '../components/GlassCard';

const QuizPage = () => {
  const [currentQuiz, setCurrentQuiz] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [score, setScore] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [timeRemaining, setTimeRemaining] = useState(300); // 5 minutes

  const quizzes = [
    {
      id: 'fundamentals',
      title: 'HVAC Fundamentals Assessment',
      description: 'Test your knowledge of basic HVAC principles and refrigeration cycle',
      difficulty: 'Beginner',
      timeLimit: 300,
      questions: [
        {
          question: 'What are the four main stages of the vapor-compression refrigeration cycle?',
          options: [
            'Compression, Condensation, Expansion, Evaporation',
            'Heating, Cooling, Ventilation, Filtration',
            'Suction, Discharge, Liquid, Vapor',
            'High Pressure, Low Pressure, Hot Gas, Cold Liquid'
          ],
          correct: 0,
          explanation: 'The vapor-compression refrigeration cycle consists of four main stages: Compression (compressor increases pressure and temperature of refrigerant vapor), Condensation (high-pressure vapor condenses to liquid in condenser, rejecting heat), Expansion (expansion device reduces pressure and temperature of liquid refrigerant), and Evaporation (low-pressure liquid evaporates in evaporator, absorbing heat from conditioned space).'
        },
        {
          question: 'What is the primary function of the evaporator coil in an air conditioning system?',
          options: [
            'To compress refrigerant vapor and increase system pressure',
            'To absorb heat from indoor air and provide cooling',
            'To reject heat to outdoor air and condense refrigerant',
            'To reduce refrigerant pressure and control flow rate'
          ],
          correct: 1,
          explanation: 'The evaporator coil\'s primary function is to absorb heat from the indoor air, providing cooling to the conditioned space. As warm indoor air passes over the cold evaporator coil, heat is transferred from the air to the refrigerant, causing the liquid refrigerant to evaporate into vapor. This process removes both sensible heat (temperature) and latent heat (moisture) from the air.'
        },
        {
          question: 'What does SEER stand for and what does it measure?',
          options: [
            'System Energy Efficiency Rating - measures annual energy consumption',
            'Seasonal Energy Efficiency Ratio - measures cooling efficiency over a season',
            'Standard Equipment Efficiency Rating - measures equipment performance',
            'Summer Energy Efficiency Ratio - measures peak cooling efficiency'
          ],
          correct: 1,
          explanation: 'SEER stands for Seasonal Energy Efficiency Ratio. It measures the cooling efficiency of an air conditioner or heat pump over an entire cooling season. SEER is calculated by dividing the total cooling output (in BTU) by the total electrical energy input (in watt-hours) during the same period. Higher SEER ratings indicate more efficient equipment, with current minimum standards requiring SEER 14-15 depending on region.'
        },
        {
          question: 'In a properly operating air conditioning system using R-410A refrigerant, what is the typical operating pressure range on the high-pressure side?',
          options: [
            '50-80 PSI',
            '120-150 PSI',
            '200-250 PSI',
            '300-400 PSI'
          ],
          correct: 2,
          explanation: 'For R-410A refrigerant systems, the typical high-pressure (discharge) side operating pressure ranges from 200-250 PSI under normal conditions (95°F outdoor temperature). This pressure corresponds to a saturation temperature of approximately 100-120°F. The exact pressure depends on outdoor ambient temperature, system load, and condenser efficiency. R-410A operates at higher pressures than older refrigerants like R-22.'
        }
      ]
    },
    {
      id: 'troubleshooting',
      title: 'System Troubleshooting',
      description: 'Identify and solve common HVAC problems using systematic diagnostic methods',
      difficulty: 'Intermediate',
      timeLimit: 450,
      questions: [
        {
          question: 'A customer reports their air conditioner is running but not cooling. The outdoor unit is operating, indoor fan is running, but the air coming from vents is warm. What is the most likely cause?',
          options: [
            'Low refrigerant charge due to system leak',
            'Dirty condenser coil reducing heat rejection',
            'Faulty thermostat not calling for cooling',
            'Blocked air filter restricting airflow'
          ],
          correct: 0,
          explanation: 'When the outdoor unit is running but not providing cooling, the most common cause is low refrigerant charge, typically due to a system leak. Low refrigerant prevents proper heat absorption in the evaporator coil, resulting in warm air from the supply vents. Other symptoms include low suction pressure, high superheat, and possible ice formation on the evaporator coil. A dirty condenser coil would cause high head pressure and reduced efficiency but would still provide some cooling.'
        },
        {
          question: 'During system diagnosis, you measure the following pressures on an R-410A system: Suction = 50 PSI, Discharge = 350 PSI. The outdoor temperature is 95°F. What condition does this indicate?',
          options: [
            'Normal operating pressures for these conditions',
            'Low refrigerant charge - both pressures are low',
            'Dirty condenser coil - high head pressure, low suction',
            'Overcharged system - both pressures are high'
          ],
          correct: 2,
          explanation: 'These pressure readings indicate a dirty condenser coil or restricted airflow across the condenser. The discharge pressure of 350 PSI is significantly higher than normal (should be 200-250 PSI at 95°F), while the suction pressure of 50 PSI is lower than normal (should be 70-80 PSI). This combination occurs when the condenser cannot reject heat effectively, causing high head pressure and reduced refrigerant flow, which lowers suction pressure.'
        },
        {
          question: 'A heat pump system is not heating effectively in winter. The outdoor unit is running, but the backup electric heat strips are energizing frequently. What should you check first?',
          options: [
            'Refrigerant charge and system pressures',
            'Defrost control board and sensors',
            'Indoor blower motor and airflow',
            'Reversing valve operation and position'
          ],
          correct: 3,
          explanation: 'When a heat pump is not heating effectively and backup heat is running frequently, the first check should be the reversing valve operation. If the reversing valve is stuck in cooling mode or not fully reversing, the system will not provide adequate heating, causing the backup heat to operate continuously. This is a common issue that can be diagnosed by checking the position of the reversing valve and measuring temperatures at the indoor and outdoor coils.'
        }
      ]
    },
    {
      id: 'codes-standards',
      title: 'Codes & Standards',
      description: 'Knowledge of industry codes, standards, and regulatory requirements',
      difficulty: 'Advanced',
      timeLimit: 600,
      questions: [
        {
          question: 'According to EPA Section 608 regulations, what is the maximum allowable leak rate for commercial refrigeration systems with a charge of 50 pounds or more?',
          options: [
            '10% annually',
            '15% annually',
            '20% annually',
            '30% annually'
          ],
          correct: 2,
          explanation: 'EPA Section 608 regulations specify that commercial refrigeration systems with 50 pounds or more of refrigerant must not exceed a 20% annual leak rate. Industrial process refrigeration systems have a 30% limit. These regulations are designed to reduce refrigerant emissions and protect the ozone layer and climate. Facilities exceeding these limits must develop and implement a leak repair plan.'
        }
      ]
    }
  ];

  const currentQuizData = quizzes[currentQuiz];
  const currentQuestionData = currentQuizData.questions[currentQuestion];
  const totalQuestions = currentQuizData.questions.length;

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
  };

  const handleNextQuestion = () => {
    const newAnswers = [...answers, selectedAnswer!];
    setAnswers(newAnswers);

    if (selectedAnswer === currentQuestionData.correct) {
      setScore(score + 1);
    }

    if (currentQuestion < totalQuestions - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setShowResult(false);
    } else {
      setShowResult(true);
    }
  };

  const resetQuiz = () => {
    setCurrentQuestion(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setScore(0);
    setAnswers([]);
    setTimeRemaining(currentQuizData.timeLimit);
  };

  const getScoreColor = () => {
    const percentage = (score / totalQuestions) * 100;
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'Intermediate': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'Advanced': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (showResult && currentQuestion >= totalQuestions - 1) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <GlassCard className="p-8 text-center">
            <div className="mb-8">
              <div className="w-20 h-20 bg-accent-100 dark:bg-accent-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="text-accent-600" size={32} />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Assessment Complete
              </h1>
              <h2 className="text-xl text-gray-600 dark:text-gray-400">
                {currentQuizData.title}
              </h2>
            </div>

            <div className="mb-8">
              <div className={`text-6xl font-bold mb-2 ${getScoreColor()}`}>
                {score}/{totalQuestions}
              </div>
              <div className="text-gray-600 dark:text-gray-400 text-lg">
                {Math.round((score / totalQuestions) * 100)}% Correct
              </div>
              <div className="mt-2 text-sm text-gray-500">
                {score >= totalQuestions * 0.8 ? 'Excellent Performance!' : 
                 score >= totalQuestions * 0.6 ? 'Good Understanding' : 
                 'Review Recommended'}
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <Target className="text-accent-600 mx-auto mb-2" size={24} />
                <div className="font-semibold text-gray-900 dark:text-white">Questions</div>
                <div className="text-gray-600 dark:text-gray-400">{totalQuestions}</div>
              </div>
              <div className="text-center">
                <CheckCircle className="text-green-600 mx-auto mb-2" size={24} />
                <div className="font-semibold text-gray-900 dark:text-white">Correct</div>
                <div className="text-gray-600 dark:text-gray-400">{score}</div>
              </div>
              <div className="text-center">
                <XCircle className="text-red-600 mx-auto mb-2" size={24} />
                <div className="font-semibold text-gray-900 dark:text-white">Incorrect</div>
                <div className="text-gray-600 dark:text-gray-400">{totalQuestions - score}</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={resetQuiz}
                className="btn-primary"
              >
                <RefreshCw size={18} className="mr-2" />
                Retake Assessment
              </button>
              <button
                onClick={() => {
                  setCurrentQuiz((currentQuiz + 1) % quizzes.length);
                  resetQuiz();
                }}
                className="btn-secondary"
              >
                <ArrowRight size={18} className="mr-2" />
                Next Assessment
              </button>
            </div>
          </GlassCard>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            HVAC Knowledge Assessment
          </h1>
          
          {/* Quiz Selection */}
          <div className="grid md:grid-cols-3 gap-4 mb-8">
            {quizzes.map((quiz, index) => (
              <button
                key={quiz.id}
                onClick={() => {
                  setCurrentQuiz(index);
                  resetQuiz();
                }}
                className={`text-left p-4 rounded-lg border transition-all duration-200 ${
                  currentQuiz === index
                    ? 'border-accent-600 bg-accent-50 dark:bg-accent-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-gray-900 dark:text-white">{quiz.title}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(quiz.difficulty)}`}>
                    {quiz.difficulty}
                  </span>
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">{quiz.description}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{quiz.questions.length} Questions</span>
                  <div className="flex items-center">
                    <Clock size={12} className="mr-1" />
                    <span>{Math.floor(quiz.timeLimit / 60)} min</span>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Quiz Interface */}
        <GlassCard className="p-8">
          {/* Progress and Timer */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Question {currentQuestion + 1} of {totalQuestions}
                </span>
                <div className="text-lg font-semibold text-gray-900 dark:text-white">
                  {currentQuizData.title}
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <Clock size={16} className="mr-1" />
                  <span>{formatTime(timeRemaining)}</span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Score: {score}/{totalQuestions}
                </div>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-accent-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentQuestion + 1) / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Question */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6 leading-relaxed">
              {currentQuestionData.question}
            </h2>

            <div className="space-y-3">
              {currentQuestionData.options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => handleAnswerSelect(index)}
                  className={`w-full text-left p-4 rounded-lg border transition-all duration-200 ${
                    selectedAnswer === index
                      ? 'border-accent-600 bg-accent-50 dark:bg-accent-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center mt-0.5 ${
                      selectedAnswer === index
                        ? 'border-accent-600 bg-accent-600'
                        : 'border-gray-300 dark:border-gray-600'
                    }`}>
                      {selectedAnswer === index && (
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      )}
                    </div>
                    <span className="text-gray-900 dark:text-white leading-relaxed">{option}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Explanation after answer selection */}
          {selectedAnswer !== null && (
            <div className="mb-8 p-4 bg-accent-50 dark:bg-accent-900/20 rounded-lg border border-accent-200 dark:border-accent-800">
              <div className="flex items-start space-x-3">
                {selectedAnswer === currentQuestionData.correct ? (
                  <CheckCircle className="text-green-600 mt-1 flex-shrink-0" size={20} />
                ) : (
                  <XCircle className="text-red-600 mt-1 flex-shrink-0" size={20} />
                )}
                <div>
                  <div className={`font-semibold mb-2 ${
                    selectedAnswer === currentQuestionData.correct ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {selectedAnswer === currentQuestionData.correct ? 'Correct Answer' : 'Incorrect Answer'}
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                    {currentQuestionData.explanation}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-end">
            <button
              onClick={handleNextQuestion}
              disabled={selectedAnswer === null}
              className="btn-primary disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <span>{currentQuestion < totalQuestions - 1 ? 'Next Question' : 'Complete Assessment'}</span>
              <ArrowRight size={18} className="ml-2" />
            </button>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};

export default QuizPage;