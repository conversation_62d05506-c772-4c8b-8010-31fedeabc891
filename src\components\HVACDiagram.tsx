import React, { useEffect, useState } from 'react';
import HeatPumpDiagram from './HeatPumpDiagram';
import PackagedRTUDiagram from './PackagedRTUDiagram';

interface HVACDiagramProps {
  systemType: string;
  isAnimating: boolean;
  onComponentClick: (component: string) => void;
  selectedComponent: string | null;
}

const HVACDiagram = ({ systemType, isAnimating, onComponentClick, selectedComponent }: HVACDiagramProps) => {
  // Route to appropriate diagram based on system type
  if (systemType === 'heat-pump') {
    return (
      <HeatPumpDiagram
        systemType={systemType}
        isAnimating={isAnimating}
        onComponentClick={onComponentClick}
        selectedComponent={selectedComponent}
      />
    );
  }

  if (systemType === 'packaged-unit') {
    return (
      <PackagedRTUDiagram
        systemType={systemType}
        isAnimating={isAnimating}
        onComponentClick={onComponentClick}
        selectedComponent={selectedComponent}
      />
    );
  }

  // Default to split system diagram
  const [animationStep, setAnimationStep] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isAnimating) {
      interval = setInterval(() => {
        setAnimationStep((prev) => (prev + 1) % 4);
      }, 1500);
    }
    return () => clearInterval(interval);
  }, [isAnimating]);

  const getComponentClass = (component: string) => {
    const baseClass = "cursor-pointer transition-all duration-200 hover:opacity-80";
    const selectedClass = selectedComponent === component ? "drop-shadow-lg" : "";
    return `${baseClass} ${selectedClass}`;
  };

  const getFlowOpacity = (step: number) => {
    if (!isAnimating) return 0.4;
    return animationStep === step ? 1 : 0.3;
  };

  return (
    <div className="w-full max-w-5xl mx-auto">
      <svg viewBox="0 0 900 650" className="w-full h-auto bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
        {/* Definitions */}
        <defs>
          {/* Enhanced Grid Pattern */}
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f1f5f9" strokeWidth="0.5" opacity="0.3"/>
            <circle cx="0" cy="0" r="0.5" fill="#e2e8f0" opacity="0.4"/>
          </pattern>

          {/* Professional Blueprint Grid */}
          <pattern id="blueprintGrid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#1e40af" strokeWidth="0.5" opacity="0.2"/>
            <path d="M 20 0 L 20 40 M 0 20 L 40 20" fill="none" stroke="#1e40af" strokeWidth="0.25" opacity="0.15"/>
          </pattern>

          {/* Enhanced Flow Gradients with Realistic Refrigerant States */}
          <linearGradient id="hotGasFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#dc2626" stopOpacity="1"/>
            <stop offset="30%" stopColor="#ef4444" stopOpacity="0.9"/>
            <stop offset="70%" stopColor="#ea580c" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#f97316" stopOpacity="0.8"/>
          </linearGradient>

          <linearGradient id="warmLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#ea580c" stopOpacity="0.9"/>
            <stop offset="50%" stopColor="#f59e0b" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#d97706" stopOpacity="0.8"/>
          </linearGradient>

          <linearGradient id="coldLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#1d4ed8" stopOpacity="0.9"/>
            <stop offset="50%" stopColor="#2563eb" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.7"/>
          </linearGradient>

          <linearGradient id="coolVaporFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#0891b2" stopOpacity="0.9"/>
            <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#0e7490" stopOpacity="0.8"/>
          </linearGradient>

          {/* 3D Component Gradients */}
          <linearGradient id="metalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f8fafc"/>
            <stop offset="30%" stopColor="#e2e8f0"/>
            <stop offset="70%" stopColor="#cbd5e1"/>
            <stop offset="100%" stopColor="#94a3b8"/>
          </linearGradient>

          <linearGradient id="compressorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6"/>
            <stop offset="30%" stopColor="#2563eb"/>
            <stop offset="70%" stopColor="#1d4ed8"/>
            <stop offset="100%" stopColor="#1e40af"/>
          </linearGradient>

          <linearGradient id="coilGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#fbbf24"/>
            <stop offset="50%" stopColor="#f59e0b"/>
            <stop offset="100%" stopColor="#d97706"/>
          </linearGradient>

          {/* Enhanced Arrow markers */}
          <marker id="arrowhead" markerWidth="10" markerHeight="8" refX="9" refY="4" orient="auto">
            <polygon points="0 0, 10 4, 0 8" fill="#374151" stroke="#1f2937" strokeWidth="0.5"/>
          </marker>

          <marker id="flowArrow" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto">
            <polygon points="0 0, 12 5, 0 10, 3 5" fill="url(#metalGradient)" stroke="#475569" strokeWidth="1"/>
          </marker>

          {/* Advanced Filters */}
          <filter id="highlight" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix values="0 0 0 0 0.23 0 0 0 0 0.51 0 0 0 0 0.96 0 0 0 1 0"/>
            <feOffset dx="0" dy="0" result="colored-blur"/>
            <feMerge>
              <feMergeNode in="colored-blur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          <filter id="shadow3d" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#000000" floodOpacity="0.3"/>
            <feDropShadow dx="1" dy="2" stdDeviation="1" floodColor="#000000" floodOpacity="0.2"/>
          </filter>

          <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feOffset dx="0" dy="0"/>
            <feGaussianBlur stdDeviation="2" result="offset-blur"/>
            <feFlood floodColor="#000000" floodOpacity="0.2"/>
            <feComposite in2="offset-blur" operator="in"/>
            <feComposite in2="SourceGraphic" operator="over"/>
          </filter>

          {/* Particle Effects for Flow Animation */}
          <circle id="particle" r="2" fill="#ffffff" opacity="0.8">
            <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
          </circle>
        </defs>
        
        {/* Background */}
        <rect width="900" height="650" fill="url(#grid)"/>

        {/* Clean Title */}
        <text x="450" y="25" textAnchor="middle" className="text-xl font-bold fill-gray-800 dark:fill-gray-200">
          Split System Air Conditioner
        </text>
        <text x="450" y="45" textAnchor="middle" className="text-sm fill-gray-600 dark:fill-gray-400">
          3-Ton Variable Speed • R-410A System
        </text>

        {/* Outdoor Unit Housing - Enhanced 3D Design */}
        <g className="outdoor-unit">
          {/* Main Unit Housing with 3D Effect */}
          <rect
            x="82" y="122" width="236" height="176"
            fill="url(#metalGradient)" stroke="#475569" strokeWidth="2"
            rx="8" filter="url(#shadow3d)"
          />
          <rect
            x="80" y="120" width="240" height="180"
            fill="none" stroke="#334155" strokeWidth="3"
            rx="8"
          />

          {/* Clean Unit Label */}
          <text x="200" y="85" textAnchor="middle" className="text-sm font-semibold fill-gray-700 dark:fill-gray-300">
            OUTDOOR UNIT
          </text>

          {/* Enhanced Condenser Coil with Realistic Fins */}
          <g className={getComponentClass('condenser')} onClick={() => onComponentClick('condenser')}>
            <rect
              x="102" y="142" width="196" height="46"
              fill="url(#coilGradient)" stroke="#d97706" strokeWidth="2"
              rx="4" filter={selectedComponent === 'condenser' ? 'url(#highlight)' : 'url(#shadow3d)'}
            />
            <rect
              x="100" y="140" width="200" height="50"
              fill="none" stroke="#b45309" strokeWidth="2"
              rx="4"
            />

            {/* Condenser Fins Detail */}
            {Array.from({length: 18}, (_, i) => (
              <line
                key={i}
                x1={110 + i * 10} y1="145"
                x2={110 + i * 10} y2="185"
                stroke="#f59e0b" strokeWidth="1" opacity="0.6"
              />
            ))}

            {/* Refrigerant Tubes */}
            <rect x="105" y="155" width="190" height="4" fill="#dc2626" rx="2" opacity="0.8"/>
            <rect x="105" y="165" width="190" height="4" fill="#dc2626" rx="2" opacity="0.8"/>
            <rect x="105" y="175" width="190" height="4" fill="#dc2626" rx="2" opacity="0.8"/>
          </g>

          <text x="200" y="210" textAnchor="middle" className="text-xs font-medium fill-gray-700 dark:fill-gray-300">
            Condenser
          </text>

          {/* Enhanced Compressor with 3D Scroll Design */}
          <g className={getComponentClass('compressor')} onClick={() => onComponentClick('compressor')}>
            {/* Compressor Base */}
            <ellipse
              cx="200" cy="255" rx="32" ry="28"
              fill="url(#compressorGradient)" stroke="#1e3a8a" strokeWidth="3"
              filter={selectedComponent === 'compressor' ? 'url(#highlight)' : 'url(#shadow3d)'}
            />
            <ellipse
              cx="200" cy="250" rx="30" ry="26"
              fill="url(#compressorGradient)" stroke="#1d4ed8" strokeWidth="2"
            />

            {/* Compressor Top */}
            <ellipse cx="200" cy="235" rx="25" ry="20" fill="#2563eb" stroke="#1e40af" strokeWidth="2"/>

            {/* Scroll Pattern */}
            <path d="M185,245 Q200,235 215,245 Q200,255 185,245"
                  fill="none" stroke="#60a5fa" strokeWidth="2" opacity="0.7"/>
            <circle cx="200" cy="245" r="8" fill="#3b82f6" stroke="#1d4ed8" strokeWidth="1"/>

            {/* Electrical Connections */}
            <rect x="220" y="240" width="15" height="8" fill="#374151" rx="2"/>
            <rect x="222" y="242" width="11" height="4" fill="#fbbf24" rx="1"/>

            {/* Suction/Discharge Ports */}
            <circle cx="175" cy="240" r="6" fill="#64748b" stroke="#475569" strokeWidth="2"/>
            <circle cx="225" cy="240" r="6" fill="#64748b" stroke="#475569" strokeWidth="2"/>
          </g>

          <text x="200" y="290" textAnchor="middle" className="text-xs font-medium fill-gray-700 dark:fill-gray-300">
            Compressor
          </text>

          {/* Enhanced Condenser Fan with Realistic Blades */}
          <g className="condenser-fan">
            <circle cx="200" cy="155" r="15" fill="url(#metalGradient)" stroke="#475569" strokeWidth="2" filter="url(#shadow3d)"/>
            <circle cx="200" cy="155" r="12" fill="#64748b" stroke="#475569" strokeWidth="1"/>

            {/* Fan Blades with Animation */}
            <g className={isAnimating ? 'animate-spin' : ''} style={{transformOrigin: '200px 155px'}}>
              {Array.from({length: 6}, (_, i) => (
                <path
                  key={i}
                  d={`M200,155 L${200 + 10 * Math.cos(i * Math.PI / 3)},${155 + 10 * Math.sin(i * Math.PI / 3)}
                      Q${200 + 8 * Math.cos((i + 0.3) * Math.PI / 3)},${155 + 8 * Math.sin((i + 0.3) * Math.PI / 3)}
                      ${200 + 6 * Math.cos((i + 0.6) * Math.PI / 3)},${155 + 6 * Math.sin((i + 0.6) * Math.PI / 3)}`}
                  fill="#334155" stroke="#1f2937" strokeWidth="0.5"
                />
              ))}
            </g>

            {/* Fan Motor */}
            <circle cx="200" cy="155" r="4" fill="#1f2937"/>
          </g>

          {/* Unit Ventilation Grilles */}
          {Array.from({length: 8}, (_, i) => (
            <rect
              key={i}
              x={90 + i * 28} y="125" width="20" height="3"
              fill="#64748b" opacity="0.6" rx="1"
            />
          ))}
        </g>

        {/* Indoor Unit Housing - Enhanced Air Handler Design */}
        <g className="indoor-unit">
          {/* Main Air Handler Housing with 3D Effect */}
          <rect
            x="582" y="202" width="236" height="116"
            fill="url(#metalGradient)" stroke="#475569" strokeWidth="2"
            rx="8" filter="url(#shadow3d)"
          />
          <rect
            x="580" y="200" width="240" height="120"
            fill="none" stroke="#334155" strokeWidth="3"
            rx="8"
          />

          {/* Clean Unit Label */}
          <text x="700" y="185" textAnchor="middle" className="text-sm font-semibold fill-gray-700 dark:fill-gray-300">
            AIR HANDLER UNIT
          </text>

          {/* Enhanced Evaporator Coil with A-Frame Design */}
          <g className={getComponentClass('evaporator')} onClick={() => onComponentClick('evaporator')}>
            {/* A-Frame Coil Structure */}
            <path
              d="M600,220 L700,220 L750,250 L700,250 L600,250 L650,220 Z"
              fill="url(#coldLiquidFlow)" stroke="#1d4ed8" strokeWidth="2"
              opacity="0.8" filter={selectedComponent === 'evaporator' ? 'url(#highlight)' : 'url(#shadow3d)'}
            />
            <path
              d="M650,220 L700,220 L700,250 L650,250 Z"
              fill="#3b82f6" stroke="#2563eb" strokeWidth="2"
              opacity="0.7"
            />

            {/* Evaporator Fins Detail */}
            {Array.from({length: 15}, (_, i) => (
              <g key={i}>
                <line
                  x1={605 + i * 9} y1="225"
                  x2={605 + i * 9} y2="245"
                  stroke="#60a5fa" strokeWidth="1" opacity="0.6"
                />
                <line
                  x1={655 + i * 6} y1="225"
                  x2={655 + i * 6} y2="245"
                  stroke="#60a5fa" strokeWidth="1" opacity="0.6"
                />
              </g>
            ))}

            {/* Refrigerant Tubes in A-Frame */}
            <path d="M605,230 Q675,225 745,235" fill="none" stroke="#0891b2" strokeWidth="3" opacity="0.8"/>
            <path d="M605,240 Q675,235 745,245" fill="none" stroke="#0891b2" strokeWidth="3" opacity="0.8"/>

            {/* Condensate Drain Pan */}
            <rect x="605" y="250" width="140" height="8" fill="#64748b" stroke="#475569" strokeWidth="1" rx="2"/>
            <circle cx="720" cy="254" r="2" fill="#374151"/>
          </g>

          <text x="700" y="275" textAnchor="middle" className="text-sm font-medium fill-gray-800 dark:fill-gray-200">
            A-Frame Evaporator
          </text>


          {/* Enhanced Variable Speed Blower */}
          <g className="blower-assembly">
            {/* Blower Housing */}
            <ellipse
              cx="700" cy="295" rx="22" ry="18"
              fill="url(#metalGradient)" stroke="#475569" strokeWidth="2"
              filter="url(#shadow3d)"
            />
            <ellipse
              cx="700" cy="295" rx="20" ry="16"
              fill="#64748b" stroke="#475569" strokeWidth="1"
            />

            {/* Centrifugal Fan Blades */}
            <g className={isAnimating ? 'animate-spin' : ''} style={{transformOrigin: '700px 295px'}}>
              {Array.from({length: 12}, (_, i) => (
                <path
                  key={i}
                  d={`M700,295 L${700 + 12 * Math.cos(i * Math.PI / 6)},${295 + 12 * Math.sin(i * Math.PI / 6)}
                      Q${700 + 10 * Math.cos((i + 0.2) * Math.PI / 6)},${295 + 10 * Math.sin((i + 0.2) * Math.PI / 6)}
                      ${700 + 8 * Math.cos((i + 0.4) * Math.PI / 6)},${295 + 8 * Math.sin((i + 0.4) * Math.PI / 6)}`}
                  fill="#334155" stroke="#1f2937" strokeWidth="0.5"
                />
              ))}
            </g>

            {/* Motor */}
            <rect x="690" y="285" width="20" height="20" fill="#1f2937" stroke="#374151" strokeWidth="1" rx="3"/>
            <circle cx="700" cy="295" r="3" fill="#fbbf24"/>

            {/* Variable Speed Drive Indicator */}
            <rect x="675" y="275" width="50" height="8" fill="#10b981" rx="2" opacity="0.8"/>
            <text x="700" y="281" textAnchor="middle" className="text-xs fill-white font-medium">VSD</text>
          </g>

          <text x="700" y="340" textAnchor="middle" className="text-xs fill-gray-600 dark:fill-gray-400">
            Variable Speed ECM Blower • 1200 CFM
          </text>

          {/* Air Filter Slot */}
          <rect x="590" y="205" width="20" height="110" fill="#e5e7eb" stroke="#9ca3af" strokeWidth="1" rx="2"/>
          <text x="600" y="260" textAnchor="middle" className="text-xs fill-gray-600" transform="rotate(-90 600 260)">
            MERV 13
          </text>

          {/* Return Air Grille */}
          {Array.from({length: 6}, (_, i) => (
            <rect
              key={i}
              x="825" y={210 + i * 15} width="15" height="8"
              fill="#64748b" opacity="0.6" rx="1"
            />
          ))}
        </g>

        {/* Enhanced Refrigerant Lines with Realistic Piping */}
        <g className="refrigerant-lines">
          {/* Discharge Line (Hot Gas - Compressor to Condenser) */}
          <g className="discharge-line">
            {/* Pipe Insulation */}
            <path
              d="M200,225 L200,188"
              fill="none"
              stroke="#f3f4f6"
              strokeWidth="12"
              opacity="0.8"
            />
            {/* Main Pipe */}
            <path
              d="M200,225 L200,188"
              fill="none"
              stroke="url(#hotGasFlow)"
              strokeWidth="8"
              opacity={getFlowOpacity(0)}
              markerEnd="url(#flowArrow)"
              filter="url(#shadow3d)"
            />
            {/* Pipe Joints */}
            <circle cx="200" cy="225" r="6" fill="#64748b" stroke="#475569" strokeWidth="1"/>
            <circle cx="200" cy="188" r="6" fill="#64748b" stroke="#475569" strokeWidth="1"/>

            {/* Temperature/Pressure Sensor */}
            <rect x="185" y="205" width="30" height="12" fill="#fbbf24" stroke="#f59e0b" strokeWidth="1" rx="2"/>
            <text x="200" y="213" textAnchor="middle" className="text-xs fill-gray-800 font-medium">T/P</text>
          </g>

          {/* Liquid Line (Warm Liquid - Condenser to Expansion Valve) */}
          <g className="liquid-line">
            {/* Pipe Insulation */}
            <path
              d="M300,165 Q450,165 565,240"
              fill="none"
              stroke="#f3f4f6"
              strokeWidth="10"
              opacity="0.8"
            />
            {/* Main Pipe */}
            <path
              d="M300,165 Q450,165 565,240"
              fill="none"
              stroke="url(#warmLiquidFlow)"
              strokeWidth="6"
              opacity={getFlowOpacity(1)}
              markerEnd="url(#flowArrow)"
              filter="url(#shadow3d)"
            />
            {/* Pipe Supports */}
            <rect x="395" y="160" width="4" height="15" fill="#64748b"/>
            <rect x="505" y="190" width="4" height="15" fill="#64748b"/>

            {/* Filter Drier */}
            <ellipse cx="420" cy="165" rx="15" ry="8" fill="#94a3b8" stroke="#64748b" strokeWidth="2"/>
            <text x="420" y="150" textAnchor="middle" className="text-xs fill-gray-700 font-medium">Filter Drier</text>

            {/* Sight Glass */}
            <circle cx="480" cy="165" r="8" fill="#e5e7eb" stroke="#6b7280" strokeWidth="2"/>
            <circle cx="480" cy="165" r="5" fill="#10b981" opacity="0.8"/>
            <text x="480" y="145" textAnchor="middle" className="text-xs fill-gray-700 font-medium">Sight Glass</text>
          </g>

          {/* Liquid Line after Expansion (Cold Liquid/Vapor Mix) */}
          <g className="expansion-line">
            <path
              d="M580,240 L600,240"
              fill="none"
              stroke="url(#coldLiquidFlow)"
              strokeWidth="6"
              opacity={getFlowOpacity(2)}
              markerEnd="url(#flowArrow)"
              filter="url(#shadow3d)"
            />
            {/* Frost indication on cold line */}
            <path
              d="M580,240 L600,240"
              fill="none"
              stroke="#ffffff"
              strokeWidth="8"
              opacity="0.3"
              strokeDasharray="2,2"
            />
          </g>

          {/* Suction Line (Cool Vapor - Evaporator to Compressor) */}
          <g className="suction-line">
            {/* Pipe Insulation (larger for suction line) */}
            <path
              d="M600,260 Q450,320 300,260 Q250,260 225,240"
              fill="none"
              stroke="#f3f4f6"
              strokeWidth="14"
              opacity="0.9"
            />
            {/* Main Pipe */}
            <path
              d="M600,260 Q450,320 300,260 Q250,260 225,240"
              fill="none"
              stroke="url(#coolVaporFlow)"
              strokeWidth="10"
              opacity={getFlowOpacity(3)}
              markerEnd="url(#flowArrow)"
              filter="url(#shadow3d)"
            />
            {/* Pipe Supports */}
            <rect x="395" y="315" width="4" height="15" fill="#64748b"/>
            <rect x="325" y="255" width="4" height="15" fill="#64748b"/>

            {/* Accumulator */}
            <ellipse cx="270" cy="250" rx="12" ry="20" fill="#64748b" stroke="#475569" strokeWidth="2"/>
            <text x="270" y="225" textAnchor="middle" className="text-xs fill-gray-700 font-medium">Accumulator</text>
          </g>

          {/* Enhanced Line Labels with Technical Data */}
          <g className="line-labels">
            <rect x="320" y="140" width="160" height="25" fill="#ffffff" stroke="#e5e7eb" strokeWidth="1" rx="3" opacity="0.9"/>
            <text x="400" y="152" textAnchor="middle" className="text-xs font-bold fill-orange-700">
              LIQUID LINE
            </text>


            <rect x="320" y="345" width="160" height="25" fill="#ffffff" stroke="#e5e7eb" strokeWidth="1" rx="3" opacity="0.9"/>
            <text x="400" y="357" textAnchor="middle" className="text-xs font-bold fill-cyan-700">
              SUCTION LINE
            </text>

          </g>

          {/* Enhanced Flow Direction Indicators with Multiple Particles */}
          {isAnimating && (
            <g className="flow-particles">
              {/* Multiple animated particles in discharge line */}
              {Array.from({length: 3}, (_, i) => (
                <circle key={`discharge-${i}`} r="3" fill="#dc2626" opacity="0.8">
                  <animateMotion
                    dur="2s"
                    repeatCount="indefinite"
                    path="M200,225 L200,188"
                    begin={`${i * 0.7}s`}
                  />
                  <animate
                    attributeName="r"
                    values="2;4;2"
                    dur="1s"
                    repeatCount="indefinite"
                    begin={`${i * 0.7}s`}
                  />
                </circle>
              ))}

              {/* Multiple animated particles in liquid line */}
              {Array.from({length: 4}, (_, i) => (
                <circle key={`liquid-${i}`} r="2" fill="#f59e0b" opacity="0.8">
                  <animateMotion
                    dur="3s"
                    repeatCount="indefinite"
                    path="M300,165 Q450,165 565,240"
                    begin={`${i * 0.75}s`}
                  />
                  <animate
                    attributeName="opacity"
                    values="0.4;1;0.4"
                    dur="1.5s"
                    repeatCount="indefinite"
                    begin={`${i * 0.75}s`}
                  />
                </circle>
              ))}

              {/* Multiple animated particles in suction line */}
              {Array.from({length: 5}, (_, i) => (
                <circle key={`suction-${i}`} r="3" fill="#0891b2" opacity="0.8">
                  <animateMotion
                    dur="4s"
                    repeatCount="indefinite"
                    path="M600,260 Q450,320 300,260 Q250,260 225,240"
                    begin={`${i * 0.8}s`}
                  />
                  <animate
                    attributeName="r"
                    values="2;4;3;2"
                    dur="2s"
                    repeatCount="indefinite"
                    begin={`${i * 0.8}s`}
                  />
                </circle>
              ))}

              {/* Temperature visualization particles */}
              <g className="temperature-particles">
                {/* Hot gas particles */}
                {Array.from({length: 6}, (_, i) => (
                  <circle key={`hot-${i}`} r="1.5" fill="#ff6b6b" opacity="0.6">
                    <animateMotion
                      dur="1.5s"
                      repeatCount="indefinite"
                      path={`M${190 + i * 3},225 L${190 + i * 3},188`}
                      begin={`${i * 0.25}s`}
                    />
                    <animate
                      attributeName="opacity"
                      values="0;0.8;0"
                      dur="1.5s"
                      repeatCount="indefinite"
                      begin={`${i * 0.25}s`}
                    />
                  </circle>
                ))}

                {/* Cold particles around evaporator */}
                {Array.from({length: 8}, (_, i) => (
                  <circle key={`cold-${i}`} r="1" fill="#4dabf7" opacity="0.5">
                    <animateMotion
                      dur="3s"
                      repeatCount="indefinite"
                      path={`M${620 + i * 15},250 Q${620 + i * 15},270 ${620 + i * 15},250`}
                      begin={`${i * 0.375}s`}
                    />
                    <animate
                      attributeName="r"
                      values="0.5;2;0.5"
                      dur="3s"
                      repeatCount="indefinite"
                      begin={`${i * 0.375}s`}
                    />
                  </circle>
                ))}
              </g>
            </g>
          )}
        </g>

        {/* Enhanced Thermostatic Expansion Valve */}
        <g className={getComponentClass('expansion-valve')} onClick={() => onComponentClick('expansion-valve')}>
          {/* TXV Body */}
          <ellipse
            cx="565" cy="240" rx="18" ry="12"
            fill="url(#metalGradient)" stroke="#d97706" strokeWidth="2"
            filter={selectedComponent === 'expansion-valve' ? 'url(#highlight)' : 'url(#shadow3d)'}
          />
          <ellipse
            cx="565" cy="240" rx="15" ry="10"
            fill="#f59e0b" stroke="#d97706" strokeWidth="1"
          />

          {/* Sensing Bulb */}
          <ellipse cx="565" cy="220" rx="8" ry="5" fill="#fbbf24" stroke="#f59e0b" strokeWidth="1"/>
          <path d="M565,225 Q565,235 565,240" stroke="#f59e0b" strokeWidth="2" fill="none"/>

          {/* Adjustment Screw */}
          <rect x="560" y="215" width="10" height="4" fill="#64748b" stroke="#475569" strokeWidth="1" rx="1"/>
          <circle cx="565" cy="217" r="1.5" fill="#374151"/>

          {/* Inlet/Outlet Connections */}
          <rect x="545" y="237" width="15" height="6" fill="#64748b" stroke="#475569" strokeWidth="1" rx="2"/>
          <rect x="570" y="237" width="15" height="6" fill="#64748b" stroke="#475569" strokeWidth="1" rx="2"/>

          {/* Equalizer Line */}
          <path d="M565,250 Q575,260 585,270" stroke="#94a3b8" strokeWidth="2" fill="none" strokeDasharray="2,2"/>
          <circle cx="585" cy="270" r="3" fill="#64748b"/>


        </g>

        <text x="565" y="190" textAnchor="middle" className="text-xs font-medium fill-gray-700 dark:fill-gray-300">
          Expansion Valve
        </text>
        <text x="565" y="285" textAnchor="middle" className="text-xs fill-gray-600 dark:fill-gray-400">
          TXV
        </text>

        {/* Pressure Gauges and Measurement Points */}
        <g className="pressure-gauges">
          {/* High Pressure Gauge */}
          <g className="high-pressure-gauge">
            <circle cx="150" cy="200" r="25" fill="#ffffff" stroke="#dc2626" strokeWidth="3"/>
            <circle cx="150" cy="200" r="20" fill="none" stroke="#ef4444" strokeWidth="1"/>

            {/* Gauge Face */}
            <text x="150" y="190" textAnchor="middle" className="text-xs font-bold fill-red-600">HP</text>
            <text x="150" y="205" textAnchor="middle" className="text-lg font-bold fill-red-700">278</text>
            <text x="150" y="215" textAnchor="middle" className="text-xs fill-red-600">PSI</text>

            {/* Gauge Needle */}
            <line
              x1="150" y1="200"
              x2={150 + 12 * Math.cos((278/400) * Math.PI - Math.PI/2)}
              y2={200 + 12 * Math.sin((278/400) * Math.PI - Math.PI/2)}
              stroke="#dc2626" strokeWidth="2"
            />

            {/* Connection Line */}
            <path d="M175,200 Q185,200 200,210" stroke="#dc2626" strokeWidth="2" strokeDasharray="3,3"/>
          </g>

          {/* Low Pressure Gauge */}
          <g className="low-pressure-gauge">
            <circle cx="750" cy="300" r="25" fill="#ffffff" stroke="#2563eb" strokeWidth="3"/>
            <circle cx="750" cy="300" r="20" fill="none" stroke="#3b82f6" strokeWidth="1"/>

            {/* Gauge Face */}
            <text x="750" y="290" textAnchor="middle" className="text-xs font-bold fill-blue-600">LP</text>
            <text x="750" y="305" textAnchor="middle" className="text-lg font-bold fill-blue-700">118</text>
            <text x="750" y="315" textAnchor="middle" className="text-xs fill-blue-600">PSI</text>

            {/* Gauge Needle */}
            <line
              x1="750" y1="300"
              x2={750 + 12 * Math.cos((118/200) * Math.PI - Math.PI/2)}
              y2={300 + 12 * Math.sin((118/200) * Math.PI - Math.PI/2)}
              stroke="#2563eb" strokeWidth="2"
            />

            {/* Connection Line */}
            <path d="M725,300 Q715,300 700,290" stroke="#2563eb" strokeWidth="2" strokeDasharray="3,3"/>
          </g>


        </g>

        {/* Refrigeration Cycle Process Indicators */}
        <g className="cycle-indicators">
          <text x="100" y="380" className="text-sm font-semibold fill-gray-800 dark:fill-gray-200">
            Refrigeration Cycle Stages:
          </text>
          
          {/* Stage 1: Compression */}
          <circle 
            cx="120" cy="410" r="8" 
            fill={animationStep === 0 ? "#dc2626" : "#94a3b8"} 
            opacity={getFlowOpacity(0)}
          />
          <text x="140" y="415" className="text-sm fill-gray-700 dark:fill-gray-300">
            1. Compression
          </text>

          {/* Stage 2: Condensation */}
          <circle
            cx="120" cy="440" r="8"
            fill={animationStep === 1 ? "#ea580c" : "#94a3b8"}
            opacity={getFlowOpacity(1)}
          />
          <text x="140" y="445" className="text-sm fill-gray-700 dark:fill-gray-300">
            2. Condensation
          </text>

          {/* Stage 3: Expansion */}
          <circle
            cx="120" cy="470" r="8"
            fill={animationStep === 2 ? "#2563eb" : "#94a3b8"}
            opacity={getFlowOpacity(2)}
          />
          <text x="140" y="475" className="text-sm fill-gray-700 dark:fill-gray-300">
            3. Expansion
          </text>

          {/* Stage 4: Evaporation */}
          <circle
            cx="120" cy="500" r="8"
            fill={animationStep === 3 ? "#0891b2" : "#94a3b8"}
            opacity={getFlowOpacity(3)}
          />
          <text x="140" y="505" className="text-sm fill-gray-700 dark:fill-gray-300">
            4. Evaporation
          </text>
        </g>

        {/* Clean System Information Panel */}
        <g className="system-info-panel">
          <rect x="400" y="380" width="280" height="120" fill="rgba(255,255,255,0.95)"
                stroke="#e5e7eb" strokeWidth="1" rx="8" filter="url(#shadow3d)"/>

          <text x="420" y="400" className="text-sm font-semibold fill-gray-800">
            Current Performance @ ARI
          </text>

          <text x="420" y="420" className="text-xs fill-gray-600">
            Cooling Capacity: 36,500 BTU/hr
          </text>
          <text x="420" y="435" className="text-xs fill-gray-600">
            Power Input: 2,650 W
          </text>
          <text x="420" y="450" className="text-xs fill-gray-600">
            EER: 12.5 • SEER: 16.0
          </text>
          <text x="420" y="465" className="text-xs fill-gray-600">
            Refrigerant: R-410A (3.9 lbs)
          </text>
          <text x="420" y="480" className="text-xs fill-gray-600">
            Airflow: 1,200 CFM
          </text>
        </g>

        {/* Enhanced Air Flow Indicators with Heat Transfer Visualization */}
        <g className="air-flow" opacity={isAnimating ? 0.8 : 0.5}>
          {/* Outdoor Air Flow with Heat Rejection Visualization */}
          <g className="outdoor-airflow">
            <path d="M150,120 L150,100" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
            <path d="M200,120 L200,100" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
            <path d="M250,120 L250,100" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
            <text x="200" y="95" textAnchor="middle" className="text-xs fill-green-600">
              Heat Rejection
            </text>

            {/* Heat rejection particles */}
            {isAnimating && Array.from({length: 12}, (_, i) => (
              <circle key={`heat-reject-${i}`} r="1.5" fill="#ff6b6b" opacity="0.6">
                <animateMotion
                  dur="2s"
                  repeatCount="indefinite"
                  path={`M${130 + i * 15},120 L${130 + i * 15},90`}
                  begin={`${i * 0.15}s`}
                />
                <animate
                  attributeName="opacity"
                  values="0;0.8;0"
                  dur="2s"
                  repeatCount="indefinite"
                  begin={`${i * 0.15}s`}
                />
              </circle>
            ))}
          </g>

          {/* Indoor Air Flow with Cooling Visualization */}
          <g className="indoor-airflow">
            <path d="M850,240 L870,240" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrowhead)"/>
            <text x="860" y="230" textAnchor="middle" className="text-xs fill-red-600">
              Return Air
            </text>

            <path d="M580,240 L560,240" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrowhead)"/>
            <text x="570" y="230" textAnchor="middle" className="text-xs fill-blue-600">
              Supply Air
            </text>

            {/* Cooling effect particles */}
            {isAnimating && Array.from({length: 8}, (_, i) => (
              <circle key={`cooling-${i}`} r="1" fill="#4dabf7" opacity="0.7">
                <animateMotion
                  dur="3s"
                  repeatCount="indefinite"
                  path={`M${580 - i * 5},240 L${560 - i * 5},240`}
                  begin={`${i * 0.2}s`}
                />
                <animate
                  attributeName="r"
                  values="0.5;2;0.5"
                  dur="1.5s"
                  repeatCount="indefinite"
                  begin={`${i * 0.2}s`}
                />
              </circle>
            ))}

            {/* Moisture removal visualization */}
            {isAnimating && Array.from({length: 6}, (_, i) => (
              <circle key={`moisture-${i}`} r="0.8" fill="#06b6d4" opacity="0.5">
                <animateMotion
                  dur="4s"
                  repeatCount="indefinite"
                  path={`M${620 + i * 20},250 L${620 + i * 20},270`}
                  begin={`${i * 0.3}s`}
                />
                <animate
                  attributeName="opacity"
                  values="0.8;0.2;0.8"
                  dur="2s"
                  repeatCount="indefinite"
                  begin={`${i * 0.3}s`}
                />
              </circle>
            ))}
          </g>
        </g>

        {/* System Performance Data */}
        <g className="performance-data">
          <rect x="650" y="380" width="200" height="140" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1" rx="4"/>
          <text x="750" y="400" textAnchor="middle" className="text-sm font-semibold fill-gray-800">
            Current Performance @ ARI
          </text>

          <text x="660" y="420" className="text-xs fill-gray-700">Cooling Capacity: 36,500 BTU/hr</text>
          <text x="660" y="435" className="text-xs fill-gray-700">Power Input: 2,850 W</text>
          <text x="660" y="450" className="text-xs fill-gray-700">Current EER: 12.8</text>
          <text x="660" y="465" className="text-xs fill-gray-700">SEER Rating: 16.0</text>
          <text x="660" y="480" className="text-xs fill-gray-700">Total Refrigerant: 3.9 lbs R-410A</text>
          <text x="660" y="495" className="text-xs fill-gray-700">Airflow: 1,200 CFM</text>
          <text x="660" y="510" className="text-xs fill-gray-700">Sound Level: 72 dB @ 10 ft</text>
        </g>

        {/* Clean Environment Labels */}
        <text x="200" y="75" textAnchor="middle" className="text-sm font-medium fill-gray-600 dark:fill-gray-400">
          Outdoor Environment
        </text>
        <text x="700" y="370" textAnchor="middle" className="text-sm font-medium fill-gray-600 dark:fill-gray-400">
          Indoor Environment
        </text>
      </svg>
    </div>
  );
};

export default HVACDiagram;