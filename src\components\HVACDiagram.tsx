import React, { useEffect, useState } from 'react';

interface HVACDiagramProps {
  systemType: string;
  isAnimating: boolean;
  onComponentClick: (component: string) => void;
  selectedComponent: string | null;
}

const HVACDiagram = ({ systemType, isAnimating, onComponentClick, selectedComponent }: HVACDiagramProps) => {
  const [animationStep, setAnimationStep] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isAnimating) {
      interval = setInterval(() => {
        setAnimationStep((prev) => (prev + 1) % 4);
      }, 1500);
    }
    return () => clearInterval(interval);
  }, [isAnimating]);

  const getComponentClass = (component: string) => {
    const baseClass = "cursor-pointer transition-all duration-200 hover:opacity-80";
    const selectedClass = selectedComponent === component ? "drop-shadow-lg" : "";
    return `${baseClass} ${selectedClass}`;
  };

  const getFlowOpacity = (step: number) => {
    if (!isAnimating) return 0.4;
    return animationStep === step ? 1 : 0.3;
  };

  return (
    <div className="w-full max-w-5xl mx-auto">
      <svg viewBox="0 0 900 650" className="w-full h-auto bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
        {/* Definitions */}
        <defs>
          {/* Grid Pattern */}
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f1f5f9" strokeWidth="1" opacity="0.5"/>
          </pattern>
          
          {/* Flow Gradients - Accurate refrigerant states */}
          <linearGradient id="hotGasFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#dc2626" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#ea580c" stopOpacity="0.9"/>
          </linearGradient>
          
          <linearGradient id="warmLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#ea580c" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#d97706" stopOpacity="0.8"/>
          </linearGradient>
          
          <linearGradient id="coldLiquidFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#2563eb" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#1d4ed8" stopOpacity="0.8"/>
          </linearGradient>
          
          <linearGradient id="coolVaporFlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#0891b2" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#0e7490" stopOpacity="0.8"/>
          </linearGradient>

          {/* Arrow markers */}
          <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
            <polygon points="0 0, 8 3, 0 6" fill="#374151"/>
          </marker>

          {/* Component selection highlight */}
          <filter id="highlight">
            <feDropShadow dx="0" dy="0" stdDeviation="3" floodColor="#3b82f6" floodOpacity="0.8"/>
          </filter>
        </defs>
        
        {/* Background */}
        <rect width="900" height="650" fill="url(#grid)"/>

        {/* Title */}
        <text x="450" y="30" textAnchor="middle" className="text-lg font-bold fill-gray-800 dark:fill-gray-200">
          Split System Air Conditioner - Refrigeration Cycle
        </text>
        <text x="450" y="50" textAnchor="middle" className="text-sm fill-gray-600 dark:fill-gray-400">
          R-410A Refrigerant System • 3-Ton Residential Unit
        </text>

        {/* Outdoor Unit Housing */}
        <g className="outdoor-unit">
          <rect 
            x="80" y="120" width="240" height="180" 
            fill="#f8fafc" stroke="#334155" strokeWidth="2" 
            rx="8"
          />
          <text x="200" y="110" textAnchor="middle" className="text-sm font-semibold fill-gray-700 dark:fill-gray-300">
            Outdoor Unit
          </text>
          
          {/* Condenser Coil */}
          <rect 
            x="100" y="140" width="200" height="50" 
            fill="#e2e8f0" stroke="#475569" strokeWidth="2" 
            rx="4"
            className={getComponentClass('condenser')}
            onClick={() => onComponentClick('condenser')}
            filter={selectedComponent === 'condenser' ? 'url(#highlight)' : ''}
          />
          <text x="200" y="170" textAnchor="middle" className="text-sm font-medium fill-gray-700">
            Condenser Coil
          </text>
          <text x="200" y="185" textAnchor="middle" className="text-xs fill-gray-600">
            Heat Rejection: 40,000 BTU/hr
          </text>
          
          {/* Compressor */}
          <circle 
            cx="200" cy="240" r="30" 
            fill="#1e40af" stroke="#1e3a8a" strokeWidth="2"
            className={getComponentClass('compressor')}
            onClick={() => onComponentClick('compressor')}
            filter={selectedComponent === 'compressor' ? 'url(#highlight)' : ''}
          />
          <text x="200" y="245" textAnchor="middle" className="text-sm font-medium fill-white">
            Compressor
          </text>
          <text x="200" y="285" textAnchor="middle" className="text-xs fill-gray-600 dark:fill-gray-400">
            3 HP Scroll Type
          </text>
          
          {/* Condenser Fan */}
          <circle cx="200" cy="155" r="12" fill="#64748b" stroke="#475569" strokeWidth="1"/>
          <path d="M190,155 L210,155 M200,145 L200,165 M195,150 L205,160 M205,150 L195,160" 
                stroke="#334155" strokeWidth="1.5"/>
        </g>

        {/* Indoor Unit Housing */}
        <g className="indoor-unit">
          <rect 
            x="580" y="200" width="240" height="120" 
            fill="#f8fafc" stroke="#334155" strokeWidth="2" 
            rx="8"
          />
          <text x="700" y="190" textAnchor="middle" className="text-sm font-semibold fill-gray-700 dark:fill-gray-300">
            Indoor Unit (Air Handler)
          </text>
          
          {/* Evaporator Coil */}
          <rect 
            x="600" y="220" width="200" height="40" 
            fill="#dbeafe" stroke="#2563eb" strokeWidth="2" 
            rx="4"
            className={getComponentClass('evaporator')}
            onClick={() => onComponentClick('evaporator')}
            filter={selectedComponent === 'evaporator' ? 'url(#highlight)' : ''}
          />
          <text x="700" y="245" textAnchor="middle" className="text-sm font-medium fill-blue-800">
            Evaporator Coil
          </text>
          <text x="700" y="255" textAnchor="middle" className="text-xs fill-blue-700">
            Heat Absorption: 36,000 BTU/hr
          </text>
          
          {/* Blower Fan */}
          <circle cx="700" cy="285" r="18" fill="#64748b" stroke="#475569" strokeWidth="1"/>
          <path d="M688,285 L712,285 M700,273 L700,297 M692,277 L708,293 M708,277 L692,293" 
                stroke="#334155" strokeWidth="1.5"/>
          <text x="700" y="340" textAnchor="middle" className="text-xs fill-gray-600 dark:fill-gray-400">
            Variable Speed Blower
          </text>
        </g>

        {/* Refrigerant Lines with Accurate Flow Representation */}
        <g className="refrigerant-lines">
          {/* Discharge Line (Hot Gas - Compressor to Condenser) */}
          <path 
            d="M200,210 L200,190" 
            fill="none" 
            stroke="url(#hotGasFlow)" 
            strokeWidth="8" 
            opacity={getFlowOpacity(0)}
            markerEnd="url(#arrowhead)"
          />
          
          {/* Liquid Line (Warm Liquid - Condenser to Expansion Valve) */}
          <path 
            d="M300,165 Q450,165 550,240" 
            fill="none" 
            stroke="url(#warmLiquidFlow)" 
            strokeWidth="8" 
            opacity={getFlowOpacity(1)}
            markerEnd="url(#arrowhead)"
          />
          
          {/* Liquid Line after Expansion (Cold Liquid/Vapor Mix) */}
          <path 
            d="M580,240 L600,240" 
            fill="none" 
            stroke="url(#coldLiquidFlow)" 
            strokeWidth="8" 
            opacity={getFlowOpacity(2)}
            markerEnd="url(#arrowhead)"
          />
          
          {/* Suction Line (Cool Vapor - Evaporator to Compressor) */}
          <path 
            d="M600,260 Q450,320 300,260 Q250,260 230,240" 
            fill="none" 
            stroke="url(#coolVaporFlow)" 
            strokeWidth="8" 
            opacity={getFlowOpacity(3)}
            markerEnd="url(#arrowhead)"
          />
          
          {/* Line Labels */}
          <text x="350" y="155" textAnchor="middle" className="text-xs font-medium fill-orange-700">
            Liquid Line (250 PSI, 110°F)
          </text>
          <text x="350" y="335" textAnchor="middle" className="text-xs font-medium fill-cyan-700">
            Suction Line (80 PSI, 50°F)
          </text>
        </g>

        {/* Expansion Valve */}
        <circle 
          cx="565" cy="240" r="15" 
          fill="#f59e0b" stroke="#d97706" strokeWidth="2"
          className={getComponentClass('expansion-valve')}
          onClick={() => onComponentClick('expansion-valve')}
          filter={selectedComponent === 'expansion-valve' ? 'url(#highlight)' : ''}
        />
        <text x="565" y="220" textAnchor="middle" className="text-xs font-medium fill-gray-700 dark:fill-gray-300">
          TXV
        </text>
        <text x="565" y="265" textAnchor="middle" className="text-xs fill-gray-600 dark:fill-gray-400">
          3-Ton Capacity
        </text>

        {/* Refrigeration Cycle Process Indicators */}
        <g className="cycle-indicators">
          <text x="100" y="380" className="text-sm font-semibold fill-gray-800 dark:fill-gray-200">
            Refrigeration Cycle Stages:
          </text>
          
          {/* Stage 1: Compression */}
          <circle 
            cx="120" cy="410" r="8" 
            fill={animationStep === 0 ? "#dc2626" : "#94a3b8"} 
            opacity={getFlowOpacity(0)}
          />
          <text x="140" y="415" className="text-sm fill-gray-700 dark:fill-gray-300">
            1. Compression (80→250 PSI, 50→180°F)
          </text>
          
          {/* Stage 2: Condensation */}
          <circle 
            cx="120" cy="440" r="8" 
            fill={animationStep === 1 ? "#ea580c" : "#94a3b8"} 
            opacity={getFlowOpacity(1)}
          />
          <text x="140" y="445" className="text-sm fill-gray-700 dark:fill-gray-300">
            2. Condensation (250 PSI, 180→110°F)
          </text>
          
          {/* Stage 3: Expansion */}
          <circle 
            cx="120" cy="470" r="8" 
            fill={animationStep === 2 ? "#2563eb" : "#94a3b8"} 
            opacity={getFlowOpacity(2)}
          />
          <text x="140" y="475" className="text-sm fill-gray-700 dark:fill-gray-300">
            3. Expansion (250→80 PSI, 110→40°F)
          </text>
          
          {/* Stage 4: Evaporation */}
          <circle 
            cx="120" cy="500" r="8" 
            fill={animationStep === 3 ? "#0891b2" : "#94a3b8"} 
            opacity={getFlowOpacity(3)}
          />
          <text x="140" y="505" className="text-sm fill-gray-700 dark:fill-gray-300">
            4. Evaporation (80 PSI, 40→50°F)
          </text>
        </g>

        {/* Air Flow Indicators */}
        <g className="air-flow" opacity={isAnimating ? 0.8 : 0.5}>
          {/* Outdoor Air Flow */}
          <path d="M150,120 L150,100" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
          <path d="M200,120 L200,100" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
          <path d="M250,120 L250,100" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
          <text x="200" y="95" textAnchor="middle" className="text-xs fill-green-600">
            Outdoor Air Flow
          </text>
          
          {/* Indoor Air Flow */}
          <path d="M850,240 L870,240" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrowhead)"/>
          <text x="860" y="230" textAnchor="middle" className="text-xs fill-red-600">
            Warm Air Return
          </text>
          
          <path d="M580,240 L560,240" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrowhead)"/>
          <text x="570" y="230" textAnchor="middle" className="text-xs fill-blue-600">
            Cool Air Supply
          </text>
        </g>

        {/* System Performance Data */}
        <g className="performance-data">
          <rect x="650" y="380" width="200" height="120" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1" rx="4"/>
          <text x="750" y="400" textAnchor="middle" className="text-sm font-semibold fill-gray-800">
            System Performance
          </text>
          
          <text x="660" y="420" className="text-xs fill-gray-700">Cooling Capacity: 36,000 BTU/hr</text>
          <text x="660" y="435" className="text-xs fill-gray-700">Power Input: 3,200 W</text>
          <text x="660" y="450" className="text-xs fill-gray-700">EER: 11.25</text>
          <text x="660" y="465" className="text-xs fill-gray-700">SEER: 16.0</text>
          <text x="660" y="480" className="text-xs fill-gray-700">Refrigerant: R-410A (6.5 lbs)</text>
        </g>

        {/* Temperature Zones */}
        <text x="200" y="80" textAnchor="middle" className="text-sm font-bold fill-red-600">
          High Temperature Zone (95°F Ambient)
        </text>
        <text x="700" y="370" textAnchor="middle" className="text-sm font-bold fill-blue-600">
          Conditioned Zone (75°F Target)
        </text>
      </svg>
    </div>
  );
};

export default HVACDiagram;