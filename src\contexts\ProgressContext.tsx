import React, { createContext, useContext, useState, useEffect } from 'react';

interface ProgressContextType {
  completedLessons: string[];
  completeLesson: (lessonId: string) => void;
  resetProgress: () => void;
}

const ProgressContext = createContext<ProgressContextType | undefined>(undefined);

export const ProgressProvider = ({ children }: { children: React.ReactNode }) => {
  const [completedLessons, setCompletedLessons] = useState<string[]>([]);

  useEffect(() => {
    // Load progress from localStorage
    const saved = localStorage.getItem('hvac-progress');
    if (saved) {
      try {
        const progress = JSON.parse(saved);
        setCompletedLessons(progress.completedLessons || []);
      } catch (error) {
        console.error('Error loading progress:', error);
      }
    }
  }, []);

  const completeLesson = (lessonId: string) => {
    setCompletedLessons(prev => {
      if (prev.includes(lessonId)) return prev;
      const updated = [...prev, lessonId];
      
      // Save to localStorage
      localStorage.setItem('hvac-progress', JSON.stringify({
        completedLessons: updated
      }));
      
      return updated;
    });
  };

  const resetProgress = () => {
    setCompletedLessons([]);
    localStorage.removeItem('hvac-progress');
  };

  return (
    <ProgressContext.Provider value={{ completedLessons, completeLesson, resetProgress }}>
      {children}
    </ProgressContext.Provider>
  );
};

export const useProgress = () => {
  const context = useContext(ProgressContext);
  if (context === undefined) {
    throw new Error('useProgress must be used within a ProgressProvider');
  }
  return context;
};