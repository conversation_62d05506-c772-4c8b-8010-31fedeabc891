import React, { useState, useEffect } from 'react';
import { Thermometer, Gauge, Activity, Zap, Droplets } from 'lucide-react';

interface MeasurementToolsProps {
  isAnimating: boolean;
  systemType: string;
  onToolClick: (tool: string) => void;
  selectedTool: string | null;
}

interface SystemData {
  highPressure: number;
  lowPressure: number;
  dischargeTemp: number;
  suctionTemp: number;
  liquidTemp: number;
  evapTemp: number;
  superheat: number;
  subcooling: number;
  powerDraw: number;
  airflow: number;
  efficiency: number;
}

const InteractiveMeasurementTools = ({ isAnimating, systemType, onToolClick, selectedTool }: MeasurementToolsProps) => {
  const [systemData, setSystemData] = useState<SystemData>({
    highPressure: 278,
    lowPressure: 118,
    dischargeTemp: 165,
    suctionTemp: 48,
    liquidTemp: 105,
    evapTemp: 40,
    superheat: 8.2,
    subcooling: 12.5,
    powerDraw: 2.85,
    airflow: 1200,
    efficiency: 12.8
  });

  // Simulate real-time data fluctuations when animating
  useEffect(() => {
    if (!isAnimating) return;

    const interval = setInterval(() => {
      setSystemData(prev => ({
        highPressure: prev.highPressure + (Math.random() - 0.5) * 4,
        lowPressure: prev.lowPressure + (Math.random() - 0.5) * 2,
        dischargeTemp: prev.dischargeTemp + (Math.random() - 0.5) * 6,
        suctionTemp: prev.suctionTemp + (Math.random() - 0.5) * 3,
        liquidTemp: prev.liquidTemp + (Math.random() - 0.5) * 4,
        evapTemp: prev.evapTemp + (Math.random() - 0.5) * 2,
        superheat: Math.max(5, Math.min(15, prev.superheat + (Math.random() - 0.5) * 1)),
        subcooling: Math.max(8, Math.min(18, prev.subcooling + (Math.random() - 0.5) * 1)),
        powerDraw: Math.max(2.5, Math.min(3.5, prev.powerDraw + (Math.random() - 0.5) * 0.1)),
        airflow: prev.airflow + (Math.random() - 0.5) * 50,
        efficiency: Math.max(11, Math.min(14, prev.efficiency + (Math.random() - 0.5) * 0.2))
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [isAnimating]);

  const getGaugeColor = (value: number, min: number, max: number, optimal: number) => {
    const distance = Math.abs(value - optimal);
    const range = max - min;
    if (distance < range * 0.1) return '#10b981'; // Green - optimal
    if (distance < range * 0.2) return '#f59e0b'; // Yellow - acceptable
    return '#ef4444'; // Red - concerning
  };

  const PressureGauge = ({ pressure, type, x, y }: { pressure: number; type: 'high' | 'low'; x: number; y: number }) => {
    const maxPressure = type === 'high' ? 400 : 200;
    const angle = (pressure / maxPressure) * 180 - 90;
    const color = type === 'high' ? '#dc2626' : '#2563eb';
    const optimalPressure = type === 'high' ? 278 : 118;
    const gaugeColor = getGaugeColor(pressure, 0, maxPressure, optimalPressure);

    return (
      <g 
        className="pressure-gauge cursor-pointer" 
        onClick={() => onToolClick(`${type}-pressure-gauge`)}
        opacity={selectedTool === `${type}-pressure-gauge` ? 1 : 0.8}
      >
        <circle cx={x} cy={y} r="30" fill="#ffffff" stroke={color} strokeWidth="3"/>
        <circle cx={x} cy={y} r="25" fill="none" stroke="#e5e7eb" strokeWidth="1"/>
        
        {/* Gauge scale */}
        {Array.from({length: 9}, (_, i) => {
          const scaleAngle = (i * 22.5 - 90) * Math.PI / 180;
          const x1 = x + 20 * Math.cos(scaleAngle);
          const y1 = y + 20 * Math.sin(scaleAngle);
          const x2 = x + 23 * Math.cos(scaleAngle);
          const y2 = y + 23 * Math.sin(scaleAngle);
          return <line key={i} x1={x1} y1={y1} x2={x2} y2={y2} stroke="#9ca3af" strokeWidth="1"/>;
        })}
        
        {/* Gauge needle */}
        <line 
          x1={x} y1={y} 
          x2={x + 18 * Math.cos(angle * Math.PI / 180)} 
          y2={y + 18 * Math.sin(angle * Math.PI / 180)} 
          stroke={gaugeColor} strokeWidth="3" strokeLinecap="round"
        />
        
        {/* Center dot */}
        <circle cx={x} cy={y} r="3" fill={color}/>
        
        {/* Digital readout */}
        <rect x={x-15} y={y+35} width="30" height="15" fill="#000000" rx="2"/>
        <text x={x} y={y+45} textAnchor="middle" className="text-xs fill-green-400 font-mono">
          {pressure.toFixed(0)}
        </text>
        
        <text x={x} y={y-40} textAnchor="middle" className="text-xs font-bold" fill={color}>
          {type === 'high' ? 'HP' : 'LP'}
        </text>
        <text x={x} y={y+60} textAnchor="middle" className="text-xs fill-gray-600">
          PSI
        </text>
      </g>
    );
  };

  const TemperatureSensor = ({ temp, label, x, y, color }: { temp: number; label: string; x: number; y: number; color: string }) => {
    const optimalTemp = label.includes('Discharge') ? 165 : label.includes('Suction') ? 48 : 
                      label.includes('Liquid') ? 105 : 40;
    const sensorColor = getGaugeColor(temp, -20, 200, optimalTemp);

    return (
      <g 
        className="temperature-sensor cursor-pointer" 
        onClick={() => onToolClick(`${label.toLowerCase()}-temp`)}
        opacity={selectedTool === `${label.toLowerCase()}-temp` ? 1 : 0.8}
      >
        <rect x={x-20} y={y-10} width="40" height="20" fill={sensorColor} stroke="#374151" strokeWidth="1" rx="4"/>
        <Thermometer size={12} x={x-15} y={y-5} className="fill-white"/>
        <text x={x+5} y={y-2} className="text-xs fill-white font-bold">
          {temp.toFixed(0)}°F
        </text>
        <text x={x} y={y+25} textAnchor="middle" className="text-xs fill-gray-700">
          {label}
        </text>
      </g>
    );
  };

  const FlowMeter = ({ flow, x, y }: { flow: number; x: number; y: number }) => {
    return (
      <g 
        className="flow-meter cursor-pointer" 
        onClick={() => onToolClick('airflow-meter')}
        opacity={selectedTool === 'airflow-meter' ? 1 : 0.8}
      >
        <circle cx={x} cy={y} r="25" fill="#ffffff" stroke="#059669" strokeWidth="2"/>
        <Activity size={16} x={x-8} y={y-15} className="fill-green-600"/>
        <text x={x} y={y+5} textAnchor="middle" className="text-sm font-bold fill-green-700">
          {flow.toFixed(0)}
        </text>
        <text x={x} y={y+35} textAnchor="middle" className="text-xs fill-gray-600">
          CFM
        </text>
      </g>
    );
  };

  const PowerMeter = ({ power, x, y }: { power: number; x: number; y: number }) => {
    return (
      <g 
        className="power-meter cursor-pointer" 
        onClick={() => onToolClick('power-meter')}
        opacity={selectedTool === 'power-meter' ? 1 : 0.8}
      >
        <rect x={x-25} y={y-15} width="50" height="30" fill="#ffffff" stroke="#f59e0b" strokeWidth="2" rx="4"/>
        <Zap size={14} x={x-20} y={y-10} className="fill-yellow-600"/>
        <text x={x+5} y={y-2} className="text-sm font-bold fill-yellow-700">
          {power.toFixed(2)}
        </text>
        <text x={x} y={y+8} textAnchor="middle" className="text-xs fill-yellow-600">
          kW
        </text>
        <text x={x} y={y+30} textAnchor="middle" className="text-xs fill-gray-600">
          Power Draw
        </text>
      </g>
    );
  };

  const EfficiencyMeter = ({ efficiency, x, y }: { efficiency: number; x: number; y: number }) => {
    const angle = ((efficiency - 8) / 8) * 180 - 90; // Scale 8-16 EER to gauge
    const efficiencyColor = getGaugeColor(efficiency, 8, 16, 13);

    return (
      <g 
        className="efficiency-meter cursor-pointer" 
        onClick={() => onToolClick('efficiency-meter')}
        opacity={selectedTool === 'efficiency-meter' ? 1 : 0.8}
      >
        <circle cx={x} cy={y} r="25" fill="#ffffff" stroke="#8b5cf6" strokeWidth="2"/>
        <circle cx={x} cy={y} r="20" fill="none" stroke="#e5e7eb" strokeWidth="1"/>
        
        {/* Efficiency scale */}
        {Array.from({length: 5}, (_, i) => {
          const scaleAngle = (i * 45 - 90) * Math.PI / 180;
          const x1 = x + 15 * Math.cos(scaleAngle);
          const y1 = y + 15 * Math.sin(scaleAngle);
          const x2 = x + 18 * Math.cos(scaleAngle);
          const y2 = y + 18 * Math.sin(scaleAngle);
          return <line key={i} x1={x1} y1={y1} x2={x2} y2={y2} stroke="#9ca3af" strokeWidth="1"/>;
        })}
        
        {/* Efficiency needle */}
        <line 
          x1={x} y1={y} 
          x2={x + 15 * Math.cos(angle * Math.PI / 180)} 
          y2={y + 15 * Math.sin(angle * Math.PI / 180)} 
          stroke={efficiencyColor} strokeWidth="2" strokeLinecap="round"
        />
        
        <circle cx={x} cy={y} r="2" fill="#8b5cf6"/>
        <text x={x} y={y+35} textAnchor="middle" className="text-xs fill-gray-600">
          EER: {efficiency.toFixed(1)}
        </text>
      </g>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg className="w-full h-full" viewBox="0 0 900 650">
        {/* Measurement Tools Overlay */}
        <g className="measurement-tools pointer-events-auto">
          {/* Pressure Gauges */}
          <PressureGauge pressure={systemData.highPressure} type="high" x={150} y={200} />
          <PressureGauge pressure={systemData.lowPressure} type="low" x={750} y={300} />
          
          {/* Temperature Sensors */}
          <TemperatureSensor temp={systemData.dischargeTemp} label="Discharge" x={225} y={205} color="#dc2626" />
          <TemperatureSensor temp={systemData.liquidTemp} label="Liquid" x={445} y={155} color="#f97316" />
          <TemperatureSensor temp={systemData.suctionTemp} label="Suction" x={335} y={320} color="#06b6d4" />
          <TemperatureSensor temp={systemData.evapTemp} label="Evaporator" x={635} y={265} color="#3b82f6" />
          
          {/* Flow and Power Meters */}
          <FlowMeter flow={systemData.airflow} x={700} y={350} />
          <PowerMeter power={systemData.powerDraw} x={200} y={350} />
          <EfficiencyMeter efficiency={systemData.efficiency} x={450} y={500} />
          
          {/* Superheat and Subcooling Indicators */}
          <g className="superheat-indicator">
            <rect x={580} y={280} width="60" height="25" fill="#10b981" stroke="#059669" strokeWidth="1" rx="3"/>
            <text x={610} y={295} textAnchor="middle" className="text-xs fill-white font-bold">
              SH: {systemData.superheat.toFixed(1)}°F
            </text>
          </g>
          
          <g className="subcooling-indicator">
            <rect x={380} y={180} width="60" height="25" fill="#3b82f6" stroke="#2563eb" strokeWidth="1" rx="3"/>
            <text x={410} y={195} textAnchor="middle" className="text-xs fill-white font-bold">
              SC: {systemData.subcooling.toFixed(1)}°F
            </text>
          </g>
        </g>
      </svg>
    </div>
  );
};

export default InteractiveMeasurementTools;
