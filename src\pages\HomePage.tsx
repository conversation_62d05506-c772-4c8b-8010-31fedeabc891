import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Eye, BookOpen, Brain, Users, Award, TrendingUp, CheckCircle } from 'lucide-react';
import GlassCard from '../components/GlassCard';

const HomePage = () => {
  const features = [
    {
      icon: Eye,
      title: 'Interactive Diagrams',
      description: 'Explore detailed HVAC system diagrams with clickable components and animated refrigerant flow visualization',
      link: '/diagrams',
      stats: '15+ Systems'
    },
    {
      icon: BookOpen,
      title: 'Technical Lessons',
      description: 'Master HVAC fundamentals through structured learning modules covering theory to advanced applications',
      link: '/lessons',
      stats: '200+ Lessons'
    },
    {
      icon: Brain,
      title: 'Knowledge Assessment',
      description: 'Test your understanding with comprehensive quizzes featuring real-world scenarios and detailed explanations',
      link: '/quiz',
      stats: '500+ Questions'
    }
  ];

  const stats = [
    { icon: Users, value: '50,000+', label: 'Active Learners' },
    { icon: BookOpen, value: '200+', label: 'Technical Modules' },
    { icon: Award, value: '95%', label: 'Completion Rate' },
    { icon: TrendingUp, value: '4.9/5', label: 'User Rating' }
  ];

  const benefits = [
    'EPA 608 Certification Preparation',
    'NATE Exam Readiness',
    'Real-world Problem Solving',
    'Industry Best Practices',
    'Code Compliance Training',
    'Energy Efficiency Focus'
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              Master HVAC with
              <span className="block text-gradient mt-2">
                Professional Training
              </span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed">
              Comprehensive HVAC education platform featuring interactive system diagrams, 
              technical documentation, and certification preparation for industry professionals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/diagrams" className="btn-primary">
                Explore Diagrams
                <ArrowRight className="ml-2 inline" size={18} />
              </Link>
              <Link to="/lessons" className="btn-secondary">
                Browse Lessons
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map(({ icon: Icon, value, label }) => (
              <div key={label} className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-accent-100 dark:bg-accent-900/30 text-accent-600 rounded-xl mb-4">
                  <Icon size={24} />
                </div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">{value}</div>
                <div className="text-gray-600 dark:text-gray-400 font-medium">{label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Professional HVAC Education
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Industry-standard training materials designed by certified professionals 
              for comprehensive skill development.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {features.map(({ icon: Icon, title, description, link, stats }, index) => (
              <Link key={title} to={link} className="group">
                <GlassCard className="p-8 h-full group-hover:shadow-xl transition-all duration-300">
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-accent-100 dark:bg-accent-900/30 text-accent-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-200">
                      <Icon size={28} />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">{title}</h3>
                    <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-6">{description}</p>
                    <div className="text-sm font-medium text-accent-600 dark:text-accent-400 mb-4">
                      {stats}
                    </div>
                    <div className="flex items-center justify-center text-accent-600 dark:text-accent-400 font-medium group-hover:gap-3 gap-2 transition-all duration-200">
                      Learn More
                      <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform duration-200" />
                    </div>
                  </div>
                </GlassCard>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Industry-Recognized Training
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
                Our curriculum aligns with industry standards and certification requirements, 
                ensuring you gain practical, applicable knowledge for real-world scenarios.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="text-accent-600 flex-shrink-0" size={20} />
                    <span className="text-gray-700 dark:text-gray-300 font-medium">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <GlassCard className="p-8">
                <div className="text-center">
                  <div className="w-20 h-20 bg-accent-100 dark:bg-accent-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Award className="text-accent-600" size={32} />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Certification Ready
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Prepare for EPA 608, NATE, and other industry certifications with 
                    our comprehensive training modules and practice assessments.
                  </p>
                  <div className="text-4xl font-bold text-accent-600 mb-2">95%</div>
                  <div className="text-gray-600 dark:text-gray-400">Pass Rate</div>
                </div>
              </GlassCard>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-accent-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Start Your HVAC Journey Today
          </h2>
          <p className="text-xl text-accent-100 mb-8 max-w-2xl mx-auto">
            Join thousands of professionals advancing their careers with our comprehensive 
            HVAC training platform.
          </p>
          <Link
            to="/diagrams"
            className="inline-flex items-center bg-white text-accent-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-50 transition-all duration-200 hover:shadow-lg"
          >
            Begin Learning
            <ArrowRight className="ml-2" size={20} />
          </Link>
        </div>
      </section>
    </div>
  );
};

export default HomePage;