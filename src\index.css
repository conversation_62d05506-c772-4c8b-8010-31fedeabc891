@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
}

@layer components {
  .glass-card {
    @apply bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50;
  }
  
  .btn-primary {
    @apply bg-accent-600 hover:bg-accent-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-accent-600/25;
  }
  
  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 font-medium px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-md;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-accent-600 to-accent-800 bg-clip-text text-transparent;
  }
}