import React, { useState } from 'react';
import { BookO<PERSON>, Clock, Star, CheckCircle, Play, Award, Filter } from 'lucide-react';
import GlassCard from '../components/GlassCard';
import { useProgress } from '../contexts/ProgressContext';

const LessonsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('fundamentals');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const { completedLessons, completeLesson } = useProgress();

  const categories = [
    { id: 'fundamentals', name: 'Fundamentals', count: 12, icon: '📚' },
    { id: 'residential', name: 'Residential Systems', count: 18, icon: '🏠' },
    { id: 'commercial', name: 'Commercial Systems', count: 15, icon: '🏢' },
    { id: 'troubleshooting', name: 'Troubleshooting', count: 22, icon: '🔧' },
    { id: 'installation', name: 'Installation', count: 16, icon: '⚙️' },
    { id: 'maintenance', name: 'Maintenance', count: 14, icon: '🛠️' }
  ];

  const lessons = {
    fundamentals: [
      {
        id: 'hvac-basics',
        title: 'HVAC System Fundamentals',
        description: 'Comprehensive overview of heating, ventilation, and air conditioning principles including system types, components, and basic operation.',
        duration: '25 min',
        difficulty: 'Beginner',
        rating: 4.9,
        topics: ['System Components', 'Basic Operation', 'Energy Efficiency', 'Safety Protocols']
      },
      {
        id: 'refrigeration-cycle',
        title: 'Refrigeration Cycle Analysis',
        description: 'Detailed examination of the four-stage refrigeration cycle including thermodynamic processes, pressure-temperature relationships, and system efficiency.',
        duration: '35 min',
        difficulty: 'Beginner',
        rating: 4.8,
        topics: ['Compression Process', 'Condensation', 'Expansion', 'Evaporation', 'P-H Diagrams']
      },
      {
        id: 'thermodynamics',
        title: 'Applied Thermodynamics',
        description: 'Heat transfer principles, temperature and pressure relationships, enthalpy calculations, and energy balance equations for HVAC applications.',
        duration: '40 min',
        difficulty: 'Intermediate',
        rating: 4.7,
        topics: ['Heat Transfer', 'Pressure-Temperature', 'Enthalpy', 'Entropy', 'Energy Balance']
      },
      {
        id: 'psychrometrics',
        title: 'Psychrometrics & Air Properties',
        description: 'Understanding moisture in air, psychrometric charts, comfort conditions, and air quality parameters affecting system design and operation.',
        duration: '30 min',
        difficulty: 'Intermediate',
        rating: 4.8,
        topics: ['Humidity Ratio', 'Wet Bulb Temperature', 'Dew Point', 'Comfort Zone', 'Air Quality']
      }
    ],
    residential: [
      {
        id: 'split-systems',
        title: 'Split System Design & Operation',
        description: 'Complete analysis of split system air conditioners including component sizing, refrigerant line design, and installation requirements.',
        duration: '30 min',
        difficulty: 'Beginner',
        rating: 4.9,
        topics: ['Indoor Unit', 'Outdoor Unit', 'Refrigerant Lines', 'Installation Standards', 'Sizing Methods']
      },
      {
        id: 'heat-pumps',
        title: 'Heat Pump Technology',
        description: 'Heat pump operation in heating and cooling modes, defrost cycles, auxiliary heat, and efficiency ratings including HSPF calculations.',
        duration: '45 min',
        difficulty: 'Intermediate',
        rating: 4.8,
        topics: ['Heating Mode', 'Cooling Mode', 'Defrost Cycle', 'HSPF Rating', 'Auxiliary Heat']
      },
      {
        id: 'ductwork-design',
        title: 'Residential Ductwork Design',
        description: 'Duct sizing methods, airflow calculations, pressure drop analysis, and installation best practices for residential applications.',
        duration: '50 min',
        difficulty: 'Advanced',
        rating: 4.7,
        topics: ['Manual D', 'Friction Rate', 'Static Pressure', 'Duct Sealing', 'Airflow Measurement']
      }
    ],
    commercial: [
      {
        id: 'rooftop-units',
        title: 'Rooftop Unit Systems',
        description: 'Commercial rooftop unit design, operation, and control systems including economizers, variable air volume, and energy recovery.',
        duration: '50 min',
        difficulty: 'Advanced',
        rating: 4.7,
        topics: ['Single Zone RTU', 'Multi Zone', 'Economizers', 'VAV Controls', 'Energy Recovery']
      },
      {
        id: 'chiller-systems',
        title: 'Chiller Plant Design',
        description: 'Chilled water systems including chiller types, cooling towers, pumping systems, and control strategies for commercial applications.',
        duration: '60 min',
        difficulty: 'Advanced',
        rating: 4.6,
        topics: ['Centrifugal Chillers', 'Cooling Towers', 'Primary/Secondary', 'VFD Controls', 'Optimization']
      }
    ],
    troubleshooting: [
      {
        id: 'diagnostic-methods',
        title: 'Systematic Diagnostic Methods',
        description: 'Structured approach to HVAC troubleshooting including testing procedures, measurement techniques, and problem isolation methods.',
        duration: '35 min',
        difficulty: 'Intermediate',
        rating: 4.9,
        topics: ['Testing Equipment', 'Safety Procedures', 'Problem Analysis', 'Root Cause', 'Documentation']
      },
      {
        id: 'refrigerant-issues',
        title: 'Refrigerant System Diagnostics',
        description: 'Identifying and resolving refrigerant-related problems including leaks, charging procedures, and system contamination issues.',
        duration: '40 min',
        difficulty: 'Advanced',
        rating: 4.8,
        topics: ['Leak Detection', 'Charging Methods', 'Contamination', 'Recovery Procedures', 'EPA Compliance']
      }
    ],
    installation: [
      {
        id: 'installation-planning',
        title: 'Installation Planning & Preparation',
        description: 'Comprehensive planning process for HVAC installations including load calculations, equipment selection, and code compliance.',
        duration: '40 min',
        difficulty: 'Intermediate',
        rating: 4.8,
        topics: ['Manual J Load Calc', 'Equipment Selection', 'Code Requirements', 'Safety Planning', 'Permits']
      },
      {
        id: 'electrical-connections',
        title: 'Electrical Installation Standards',
        description: 'Proper electrical connections, wiring methods, control circuits, and safety procedures for HVAC equipment installation.',
        duration: '45 min',
        difficulty: 'Advanced',
        rating: 4.7,
        topics: ['NEC Requirements', 'Control Wiring', 'Disconnect Sizing', 'Grounding', 'Testing Procedures']
      }
    ],
    maintenance: [
      {
        id: 'preventive-maintenance',
        title: 'Preventive Maintenance Programs',
        description: 'Developing comprehensive maintenance schedules, inspection procedures, and documentation systems for optimal system performance.',
        duration: '30 min',
        difficulty: 'Beginner',
        rating: 4.9,
        topics: ['Maintenance Schedules', 'Inspection Checklists', 'Documentation', 'Performance Tracking', 'Cost Analysis']
      },
      {
        id: 'filter-maintenance',
        title: 'Air Filtration & Indoor Air Quality',
        description: 'Filter types, MERV ratings, replacement schedules, and indoor air quality improvement strategies for residential and commercial systems.',
        duration: '25 min',
        difficulty: 'Beginner',
        rating: 4.8,
        topics: ['Filter Types', 'MERV Ratings', 'Pressure Drop', 'IAQ Standards', 'Replacement Schedules']
      }
    ]
  };

  const currentLessons = lessons[selectedCategory as keyof typeof lessons] || [];
  const filteredLessons = difficultyFilter === 'all' 
    ? currentLessons 
    : currentLessons.filter(lesson => lesson.difficulty === difficultyFilter);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'Intermediate': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'Advanced': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const isCompleted = (lessonId: string) => completedLessons.includes(lessonId);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Technical Learning Modules
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl">
            Industry-standard HVAC education covering fundamental principles through advanced applications. 
            All content aligned with EPA, NATE, and ASHRAE standards.
          </p>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Categories */}
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                      selectedCategory === category.id
                        ? 'bg-accent-600 text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="font-medium">{category.name}</span>
                      </div>
                      <span className="text-sm opacity-75">{category.count}</span>
                    </div>
                  </button>
                ))}
              </div>
            </GlassCard>

            {/* Difficulty Filter */}
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <Filter size={18} className="mr-2" />
                Difficulty Level
              </h3>
              <div className="space-y-2">
                {['all', 'Beginner', 'Intermediate', 'Advanced'].map((level) => (
                  <button
                    key={level}
                    onClick={() => setDifficultyFilter(level)}
                    className={`w-full text-left p-2 rounded-lg text-sm transition-all duration-200 ${
                      difficultyFilter === level
                        ? 'bg-accent-100 text-accent-700 dark:bg-accent-900/30 dark:text-accent-400'
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                    }`}
                  >
                    {level === 'all' ? 'All Levels' : level}
                  </button>
                ))}
              </div>
            </GlassCard>

            {/* Progress Stats */}
            <GlassCard className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Learning Progress</h3>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-accent-600">{completedLessons.length}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Lessons Completed</div>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <Award size={16} />
                  <span>Continue learning to unlock certifications</span>
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Lessons Grid */}
          <div className="lg:col-span-3">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {categories.find(c => c.id === selectedCategory)?.name} 
                <span className="text-gray-500 ml-2">({filteredLessons.length} lessons)</span>
              </h2>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {filteredLessons.map((lesson) => (
                <GlassCard key={lesson.id} className="p-6 hover:shadow-lg transition-all duration-300">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {lesson.title}
                        </h3>
                        {isCompleted(lesson.id) && (
                          <CheckCircle className="text-green-500 flex-shrink-0" size={20} />
                        )}
                      </div>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(lesson.difficulty)}`}>
                        {lesson.difficulty}
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm leading-relaxed">
                    {lesson.description}
                  </p>

                  {/* Topics */}
                  <div className="mb-4">
                    <div className="text-xs font-medium text-gray-900 dark:text-white mb-2">Key Topics:</div>
                    <div className="flex flex-wrap gap-1">
                      {lesson.topics.map((topic, index) => (
                        <span key={index} className="inline-block bg-accent-100 dark:bg-accent-900/30 text-accent-700 dark:text-accent-400 text-xs px-2 py-1 rounded">
                          {topic}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Lesson Stats */}
                  <div className="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <div className="flex items-center space-x-1">
                      <Clock size={14} />
                      <span>{lesson.duration}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="text-yellow-500" size={14} />
                      <span>{lesson.rating}</span>
                    </div>
                  </div>

                  {/* Action Button */}
                  <button
                    onClick={() => completeLesson(lesson.id)}
                    className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                      isCompleted(lesson.id)
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : 'bg-accent-600 text-white hover:bg-accent-700'
                    }`}
                  >
                    {isCompleted(lesson.id) ? (
                      <>
                        <CheckCircle size={18} />
                        <span>Completed</span>
                      </>
                    ) : (
                      <>
                        <Play size={18} />
                        <span>Start Lesson</span>
                      </>
                    )}
                  </button>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LessonsPage;